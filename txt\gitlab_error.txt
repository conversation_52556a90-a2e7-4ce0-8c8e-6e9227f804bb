[0KRunning with gitlab-runner 17.10.2 (b231051c)[0;m
[0K  on GLaaS-Shared-Runner-AWSEKS-Prod R3XAexgnb, system ID: r_C4rY1asbV0Vn[0;m
[0K[36;1mResolving secrets[0;m[0;m
section_start:1751886746:prepare_executor
[0K[0K[36;1mPreparing the "kubernetes" executor[0;m[0;m
[0KUsing Kubernetes namespace: glrunner[0;m
[0KUsing Kubernetes executor with image registry.gitlab.ea.com/dre-cobra/container-images/python-dre-cobra:latest_37 ...[0;m
[0KUsing attach strategy to execute scripts...[0;m
section_end:1751886746:prepare_executor
[0Ksection_start:1751886746:prepare_script
[0K[0K[36;1mPreparing environment[0;m[0;m
[0KUsing FF_USE_POD_ACTIVE_DEADLINE_SECONDS, the Pod activeDeadlineSeconds will be set to the job timeout: 1h0m0s...[0;m
Waiting for pod glrunner/runner-r3xaexgnb-project-1765-concurrent-11-vt5a6ft8 to be running, status is Pending
Running on runner-r3xaexgnb-project-1765-concurrent-11-vt5a6ft8 via gitlab-runner-64dd9cdc4d-vrnz7...

section_end:1751886750:prepare_script
[0Ksection_start:1751886750:get_sources
[0K[0K[36;1mGetting source from Git repository[0;m[0;m
[32;1mFetching changes with git depth set to 1...[0;m
Initialized empty Git repository in /builds/dre-cobra/elipy/elipy2/.git/
[32;1mCreated fresh repository.[0;m
[32;1mChecking out e786a931 as detached HEAD (ref is hvu-cobra-6811-debug-bct-deletion)...[0;m

[32;1mSkipping Git submodules setup[0;m

section_end:1751886751:get_sources
[0Ksection_start:1751886751:step_script
[0K[0K[36;1mExecuting "step_script" stage of the job script[0;m[0;m
[32;1m$ pip3 install -i $ARTIFACTORY_URL -r requirements.txt[0;m
Looking in indexes: https://artifactory.eu.ea.com/artifactory/api/pypi/dreeu-pypi-virtual/simple, https://pypi.python.org/simple
Collecting azure-storage-file-share==12.15.0
  Downloading azure_storage_file_share-12.15.0-py3-none-any.whl (267 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 267.0/267.0 kB 8.7 MB/s eta 0:00:00
Collecting black==23.3.0
  Downloading black-23.3.0-py3-none-any.whl (180 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 181.0/181.0 kB 31.7 MB/s eta 0:00:00
Collecting boto3==1.26.132
  Downloading boto3-1.26.132-py3-none-any.whl (135 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 135.6/135.6 kB 25.8 MB/s eta 0:00:00
Collecting click==8.1.3
  Downloading click-8.1.3-py3-none-any.whl (96 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.6/96.6 kB 20.5 MB/s eta 0:00:00
Collecting Deprecated==1.2.13
  Downloading Deprecated-1.2.13-py2.py3-none-any.whl (9.6 kB)
Collecting docutils==0.18.1
  Downloading docutils-0.18.1-py2.py3-none-any.whl (570 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 570.0/570.0 kB 38.5 MB/s eta 0:00:00
Collecting editorconfig-checker==2.7.1
  Downloading editorconfig-checker-2.7.1.tar.gz (4.7 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting elasticsearch==7.17.9
  Downloading elasticsearch-7.17.9-py2.py3-none-any.whl (385 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 386.0/386.0 kB 46.3 MB/s eta 0:00:00
Collecting future==0.18.3
  Downloading future-0.18.3.tar.gz (840 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 840.9/840.9 kB 74.3 MB/s eta 0:00:00
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting hvac==1.1.0
  Downloading hvac-1.1.0-py3-none-any.whl (144 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 144.9/144.9 kB 28.9 MB/s eta 0:00:00
Collecting m2r2==0.3.2
  Downloading m2r2-0.3.2-py3-none-any.whl (11 kB)
Collecting mistune==0.8.4
  Downloading mistune-0.8.4-py2.py3-none-any.whl (16 kB)
Collecting mock==5.0.2
  Downloading mock-5.0.2-py3-none-any.whl (30 kB)
Collecting moto==4.1.9
  Downloading moto-4.1.9-py2.py3-none-any.whl (3.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.0/3.0 MB 107.9 MB/s eta 0:00:00
Collecting psutil==5.9.5
  Downloading psutil-5.9.5.tar.gz (493 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 493.5/493.5 kB 59.6 MB/s eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pylint==2.17.4
  Downloading pylint-2.17.4-py3-none-any.whl (536 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 536.6/536.6 kB 56.8 MB/s eta 0:00:00
Collecting pytest-cov==4.0.0
  Downloading pytest_cov-4.0.0-py3-none-any.whl (21 kB)
Collecting pytest-env==0.8.1
  Downloading pytest_env-0.8.1-py3-none-any.whl (5.2 kB)
Collecting pytest-mock==3.10.0
  Downloading pytest_mock-3.10.0-py3-none-any.whl (9.3 kB)
Collecting pytest-runner==6.0.0
  Downloading pytest_runner-6.0.0-py3-none-any.whl (7.2 kB)
Collecting pytest-timeout==2.1.0
  Downloading pytest_timeout-2.1.0-py3-none-any.whl (12 kB)
Collecting pytest-xdist==3.2.1
  Downloading pytest_xdist-3.2.1-py3-none-any.whl (41 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 41.0/41.0 kB 8.7 MB/s eta 0:00:00
Collecting pytest==7.3.1
  Downloading pytest-7.3.1-py3-none-any.whl (320 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 320.5/320.5 kB 44.8 MB/s eta 0:00:00
Collecting PyYAML==6.0.1
  Downloading PyYAML-6.0.1.tar.gz (125 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 125.2/125.2 kB 25.9 MB/s eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting requests-mock==1.10.0
  Downloading requests_mock-1.10.0-py2.py3-none-any.whl (28 kB)
Collecting requests==2.30.0
  Downloading requests-2.30.0-py3-none-any.whl (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.5/62.5 kB 13.7 MB/s eta 0:00:00
Requirement already satisfied: retry==0.9.2 in /usr/local/lib/python3.7/site-packages (from -r requirements.txt (line 30)) (0.9.2)
Collecting sentry-sdk==1.22.2
  Downloading sentry_sdk-1.22.2-py2.py3-none-any.whl (203 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 203.3/203.3 kB 42.0 MB/s eta 0:00:00
Collecting setuptools==67.7.2
  Downloading setuptools-67.7.2-py3-none-any.whl (1.1 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.1/1.1 MB 89.0 MB/s eta 0:00:00
Collecting six==1.16.0
  Downloading six-1.16.0-py2.py3-none-any.whl (11 kB)
Collecting snowballstemmer==2.2.0
  Downloading snowballstemmer-2.2.0-py2.py3-none-any.whl (93 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 93.0/93.0 kB 21.0 MB/s eta 0:00:00
Collecting sphinx-rtd-theme==1.2.0
  Downloading sphinx_rtd_theme-1.2.0-py2.py3-none-any.whl (2.8 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.8/2.8 MB 96.0 MB/s eta 0:00:00
Collecting Sphinx==5.3.0
  Downloading sphinx-5.3.0-py3-none-any.whl (3.2 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.2/3.2 MB 105.6 MB/s eta 0:00:00
Collecting tox==4.5.1
  Downloading tox-4.5.1-py3-none-any.whl (148 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 149.0/149.0 kB 37.0 MB/s eta 0:00:00
Collecting virtualenv==20.26.3
  Downloading virtualenv-20.26.3-py3-none-any.whl (5.7 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.7/5.7 MB 115.8 MB/s eta 0:00:00
Collecting isodate>=0.6.1
  Downloading isodate-0.7.2-py3-none-any.whl (22 kB)
Requirement already satisfied: cryptography>=2.1.4 in /usr/local/lib/python3.7/site-packages (from azure-storage-file-share==12.15.0->-r requirements.txt (line 4)) (40.0.2)
Collecting azure-core<2.0.0,>=1.28.0
  Downloading azure_core-1.30.1-py3-none-any.whl (193 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 193.4/193.4 kB 45.3 MB/s eta 0:00:00
Requirement already satisfied: typing-extensions>=4.3.0 in /usr/local/lib/python3.7/site-packages (from azure-storage-file-share==12.15.0->-r requirements.txt (line 4)) (4.5.0)
Requirement already satisfied: pathspec>=0.9.0 in /usr/local/lib/python3.7/site-packages (from black==23.3.0->-r requirements.txt (line 5)) (0.11.1)
Requirement already satisfied: typed-ast>=1.4.2 in /usr/local/lib/python3.7/site-packages (from black==23.3.0->-r requirements.txt (line 5)) (1.4.3)
Requirement already satisfied: tomli>=1.1.0 in /usr/local/lib/python3.7/site-packages (from black==23.3.0->-r requirements.txt (line 5)) (1.2.3)
Requirement already satisfied: mypy-extensions>=0.4.3 in /usr/local/lib/python3.7/site-packages (from black==23.3.0->-r requirements.txt (line 5)) (1.0.0)
Requirement already satisfied: platformdirs>=2 in /usr/local/lib/python3.7/site-packages (from black==23.3.0->-r requirements.txt (line 5)) (3.5.0)
Requirement already satisfied: packaging>=22.0 in /usr/local/lib/python3.7/site-packages (from black==23.3.0->-r requirements.txt (line 5)) (23.1)
Collecting botocore<1.30.0,>=1.29.132
  Downloading botocore-1.29.165-py3-none-any.whl (11.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 11.0/11.0 MB 103.8 MB/s eta 0:00:00
Collecting s3transfer<0.7.0,>=0.6.0
  Downloading s3transfer-0.6.2-py3-none-any.whl (79 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 79.8/79.8 kB 23.6 MB/s eta 0:00:00
Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /usr/local/lib/python3.7/site-packages (from boto3==1.26.132->-r requirements.txt (line 6)) (1.0.1)
Requirement already satisfied: importlib-metadata in /usr/local/lib/python3.7/site-packages (from click==8.1.3->-r requirements.txt (line 7)) (6.6.0)
Requirement already satisfied: wrapt<2,>=1.10 in /usr/local/lib/python3.7/site-packages (from Deprecated==1.2.13->-r requirements.txt (line 8)) (1.12.1)
Requirement already satisfied: urllib3<2,>=1.21.1 in /usr/local/lib/python3.7/site-packages (from elasticsearch==7.17.9->-r requirements.txt (line 11)) (1.26.9)
Requirement already satisfied: certifi in /usr/local/lib/python3.7/site-packages (from elasticsearch==7.17.9->-r requirements.txt (line 11)) (2023.5.7)
Collecting pyhcl<0.5.0,>=0.4.4
  Downloading pyhcl-0.4.5-py3-none-any.whl (50 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 50.2/50.2 kB 15.6 MB/s eta 0:00:00
Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /usr/local/lib/python3.7/site-packages (from moto==4.1.9->-r requirements.txt (line 17)) (2.8.2)
Requirement already satisfied: xmltodict in /usr/local/lib/python3.7/site-packages (from moto==4.1.9->-r requirements.txt (line 17)) (0.13.0)
Requirement already satisfied: responses>=0.13.0 in /usr/local/lib/python3.7/site-packages (from moto==4.1.9->-r requirements.txt (line 17)) (0.23.1)
Requirement already satisfied: Jinja2>=2.10.1 in /usr/local/lib/python3.7/site-packages (from moto==4.1.9->-r requirements.txt (line 17)) (3.1.2)
Requirement already satisfied: werkzeug!=2.2.0,!=2.2.1,>=0.5 in /usr/local/lib/python3.7/site-packages (from moto==4.1.9->-r requirements.txt (line 17)) (2.2.3)
Collecting tomlkit>=0.10.1
  Downloading tomlkit-0.12.5-py3-none-any.whl (37 kB)
Requirement already satisfied: isort<6,>=4.2.5 in /usr/local/lib/python3.7/site-packages (from pylint==2.17.4->-r requirements.txt (line 19)) (5.11.5)
Collecting dill>=0.2
  Downloading dill-0.3.7-py3-none-any.whl (115 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 115.3/115.3 kB 33.5 MB/s eta 0:00:00
Requirement already satisfied: mccabe<0.8,>=0.6 in /usr/local/lib/python3.7/site-packages (from pylint==2.17.4->-r requirements.txt (line 19)) (0.6.1)
Collecting astroid<=2.17.0-dev0,>=2.15.4
  Downloading astroid-2.15.8-py3-none-any.whl (278 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 278.3/278.3 kB 57.8 MB/s eta 0:00:00
Requirement already satisfied: coverage[toml]>=5.2.1 in /usr/local/lib/python3.7/site-packages (from pytest-cov==4.0.0->-r requirements.txt (line 20)) (7.2.5)
Requirement already satisfied: execnet>=1.1 in /usr/local/lib/python3.7/site-packages (from pytest-xdist==3.2.1->-r requirements.txt (line 25)) (1.9.0)
Collecting iniconfig
  Downloading iniconfig-2.0.0-py3-none-any.whl (5.9 kB)
Requirement already satisfied: pluggy<2.0,>=0.12 in /usr/local/lib/python3.7/site-packages (from pytest==7.3.1->-r requirements.txt (line 26)) (0.13.1)
Collecting exceptiongroup>=1.0.0rc8
  Downloading exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.7/site-packages (from requests==2.30.0->-r requirements.txt (line 29)) (3.4)
Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.7/site-packages (from requests==2.30.0->-r requirements.txt (line 29)) (2.0.12)
Requirement already satisfied: decorator>=3.4.2 in /usr/local/lib/python3.7/site-packages (from retry==0.9.2->-r requirements.txt (line 30)) (5.1.1)
Requirement already satisfied: py<2.0.0,>=1.4.26 in /usr/local/lib/python3.7/site-packages (from retry==0.9.2->-r requirements.txt (line 30)) (1.11.0)
Collecting urllib3<2,>=1.21.1
  Downloading urllib3-1.26.20-py2.py3-none-any.whl (144 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 144.2/144.2 kB 41.9 MB/s eta 0:00:00
Collecting sphinxcontrib-jquery!=3.0.0,>=2.0.0
  Downloading sphinxcontrib_jquery-4.1-py2.py3-none-any.whl (121 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 121.1/121.1 kB 6.5 MB/s eta 0:00:00
Collecting sphinxcontrib-jsmath
  Downloading sphinxcontrib_jsmath-1.0.1-py2.py3-none-any.whl (5.1 kB)
Collecting sphinxcontrib-applehelp
  Downloading sphinxcontrib_applehelp-1.0.2-py2.py3-none-any.whl (121 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 121.2/121.2 kB 32.2 MB/s eta 0:00:00
Collecting imagesize>=1.3
  Downloading imagesize-1.4.1-py2.py3-none-any.whl (8.8 kB)
Collecting sphinxcontrib-serializinghtml>=1.1.5
  Downloading sphinxcontrib_serializinghtml-1.1.5-py2.py3-none-any.whl (94 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 94.0/94.0 kB 26.7 MB/s eta 0:00:00
Collecting sphinxcontrib-htmlhelp>=2.0.0
  Downloading sphinxcontrib_htmlhelp-2.0.0-py2.py3-none-any.whl (100 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.5/100.5 kB 25.1 MB/s eta 0:00:00
Collecting alabaster<0.8,>=0.7
  Downloading alabaster-0.7.13-py3-none-any.whl (13 kB)
Collecting babel>=2.9
  Downloading Babel-2.14.0-py3-none-any.whl (11.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 11.0/11.0 MB 97.6 MB/s eta 0:00:00
Collecting sphinxcontrib-qthelp
  Downloading sphinxcontrib_qthelp-1.0.3-py2.py3-none-any.whl (90 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 90.6/90.6 kB 27.8 MB/s eta 0:00:00
Collecting sphinxcontrib-devhelp
  Downloading sphinxcontrib_devhelp-1.0.2-py2.py3-none-any.whl (84 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.7/84.7 kB 25.3 MB/s eta 0:00:00
Collecting Pygments>=2.12
  Downloading pygments-2.17.2-py3-none-any.whl (1.2 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 64.0 MB/s eta 0:00:00
Collecting cachetools>=5.3
  Downloading cachetools-5.5.2-py3-none-any.whl (10 kB)
Requirement already satisfied: filelock>=3.11 in /usr/local/lib/python3.7/site-packages (from tox==4.5.1->-r requirements.txt (line 37)) (3.12.0)
Collecting tomli>=1.1.0
  Downloading tomli-2.0.1-py3-none-any.whl (12 kB)
Collecting colorama>=0.4.6
  Downloading colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Collecting pyproject-api>=1.5.1
  Downloading pyproject_api-1.5.3-py3-none-any.whl (12 kB)
Collecting pluggy<2.0,>=0.12
  Downloading pluggy-1.2.0-py3-none-any.whl (17 kB)
Collecting chardet>=5.1
  Downloading chardet-5.2.0-py3-none-any.whl (199 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 199.4/199.4 kB 44.7 MB/s eta 0:00:00
Collecting filelock>=3.11
  Downloading filelock-3.12.2-py3-none-any.whl (10 kB)
Collecting platformdirs>=2
  Downloading platformdirs-4.0.0-py3-none-any.whl (17 kB)
Collecting distlib<1,>=0.3.7
  Downloading distlib-0.3.9-py2.py3-none-any.whl (468 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 77.4 MB/s eta 0:00:00
Requirement already satisfied: lazy-object-proxy>=1.4.0 in /usr/local/lib/python3.7/site-packages (from astroid<=2.17.0-dev0,>=2.15.4->pylint==2.17.4->-r requirements.txt (line 19)) (1.9.0)
Collecting typing-extensions>=4.3.0
  Downloading typing_extensions-4.7.1-py3-none-any.whl (33 kB)
Requirement already satisfied: pytz>=2015.7 in /usr/local/lib/python3.7/site-packages (from babel>=2.9->Sphinx==5.3.0->-r requirements.txt (line 36)) (2023.3)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.7/site-packages (from cryptography>=2.1.4->azure-storage-file-share==12.15.0->-r requirements.txt (line 4)) (1.15.1)
Requirement already satisfied: zipp>=0.5 in /usr/local/lib/python3.7/site-packages (from importlib-metadata->click==8.1.3->-r requirements.txt (line 7)) (3.15.0)
Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.7/site-packages (from Jinja2>=2.10.1->moto==4.1.9->-r requirements.txt (line 17)) (2.1.2)
Requirement already satisfied: types-PyYAML in /usr/local/lib/python3.7/site-packages (from responses>=0.13.0->moto==4.1.9->-r requirements.txt (line 17)) (********)
Requirement already satisfied: pycparser in /usr/local/lib/python3.7/site-packages (from cffi>=1.12->cryptography>=2.1.4->azure-storage-file-share==12.15.0->-r requirements.txt (line 4)) (2.21)
Building wheels for collected packages: editorconfig-checker, future, psutil, PyYAML
  Building wheel for editorconfig-checker (setup.py): started
  Building wheel for editorconfig-checker (setup.py): finished with status 'done'
  Created wheel for editorconfig-checker: filename=editorconfig_checker-2.7.1-py2.py3-none-linux_x86_64.whl size=2540074 sha256=20260de478f32d6dc7ae6c86ee20b44c90b54eec77f3dbebf36dfb155fb99467
  Stored in directory: /root/.cache/pip/wheels/51/e4/1e/3c2a30617f14bd079a9f1da295610d3609590bfcef2e9aeeb5
  Building wheel for future (setup.py): started
  Building wheel for future (setup.py): finished with status 'done'
  Created wheel for future: filename=future-0.18.3-py3-none-any.whl size=494260 sha256=11cbdb475e8b0a7aac3243cf243c6a3d96e658fb41b721f690e3c0fac834edd7
  Stored in directory: /root/.cache/pip/wheels/fa/cd/1f/c6b7b50b564983bf3011e8fc75d06047ddc50c07f6e3660b00
  Building wheel for psutil (pyproject.toml): started
  Building wheel for psutil (pyproject.toml): finished with status 'done'
  Created wheel for psutil: filename=psutil-5.9.5-cp37-abi3-linux_x86_64.whl size=234997 sha256=07e8bb0aabaf166c1702fa3bdb2a9fd194d924cf127d5d0564e6f69c268007a8
  Stored in directory: /root/.cache/pip/wheels/eb/6f/78/5e9933ec5c31dca722dedbd58a9bb3ecb5c24a4edec34ac81e
  Building wheel for PyYAML (pyproject.toml): started
  Building wheel for PyYAML (pyproject.toml): finished with status 'done'
  Created wheel for PyYAML: filename=PyYAML-6.0.1-cp37-cp37m-linux_x86_64.whl size=45364 sha256=d1bd9e9ad898d30d5388009585e9f33a7ffacdc4fd134e2bc6d11e13dcfdf18a
  Stored in directory: /root/.cache/pip/wheels/0e/39/52/718d4adc87676e8afd97d5830cd75ae7d847e540fbf446665c
Successfully built editorconfig-checker future psutil PyYAML
Installing collected packages: snowballstemmer, pyhcl, mistune, distlib, urllib3, typing-extensions, tomlkit, tomli, sphinxcontrib-serializinghtml, sphinxcontrib-qthelp, sphinxcontrib-jsmath, sphinxcontrib-htmlhelp, sphinxcontrib-devhelp, sphinxcontrib-applehelp, six, setuptools, PyYAML, pytest-runner, Pygments, psutil, mock, isodate, iniconfig, imagesize, future, filelock, editorconfig-checker, docutils, dill, Deprecated, colorama, chardet, cachetools, babel, alabaster, sentry-sdk, requests, pyproject-api, platformdirs, m2r2, exceptiongroup, elasticsearch, astroid, virtualenv, Sphinx, requests-mock, pylint, pluggy, hvac, click, botocore, azure-core, tox, sphinxcontrib-jquery, s3transfer, pytest, black, azure-storage-file-share, sphinx-rtd-theme, pytest-xdist, pytest-timeout, pytest-mock, pytest-env, pytest-cov, boto3, moto
  Attempting uninstall: distlib
    Found existing installation: distlib 0.3.6
    Uninstalling distlib-0.3.6:
      Successfully uninstalled distlib-0.3.6
  Attempting uninstall: urllib3
    Found existing installation: urllib3 1.26.9
    Uninstalling urllib3-1.26.9:
      Successfully uninstalled urllib3-1.26.9
  Attempting uninstall: typing-extensions
    Found existing installation: typing_extensions 4.5.0
    Uninstalling typing_extensions-4.5.0:
      Successfully uninstalled typing_extensions-4.5.0
  Attempting uninstall: tomli
    Found existing installation: tomli 1.2.3
    Uninstalling tomli-1.2.3:
      Successfully uninstalled tomli-1.2.3
  Attempting uninstall: six
    Found existing installation: six 1.12.0
    Uninstalling six-1.12.0:
      Successfully uninstalled six-1.12.0
  Attempting uninstall: setuptools
    Found existing installation: setuptools 60.5.0
    Uninstalling setuptools-60.5.0:
      Successfully uninstalled setuptools-60.5.0
  Attempting uninstall: PyYAML
    Found existing installation: PyYAML 3.13
    Uninstalling PyYAML-3.13:
      Successfully uninstalled PyYAML-3.13
  Attempting uninstall: pytest-runner
    Found existing installation: pytest-runner 3.0
    Uninstalling pytest-runner-3.0:
      Successfully uninstalled pytest-runner-3.0
  Attempting uninstall: psutil
    Found existing installation: psutil 5.6.3
    Uninstalling psutil-5.6.3:
      Successfully uninstalled psutil-5.6.3
  Attempting uninstall: mock
    Found existing installation: mock 2.0.0
    Uninstalling mock-2.0.0:
      Successfully uninstalled mock-2.0.0
  Attempting uninstall: future
    Found existing installation: future 0.17.1
    Uninstalling future-0.17.1:
      Successfully uninstalled future-0.17.1
  Attempting uninstall: filelock
    Found existing installation: filelock 3.12.0
    Uninstalling filelock-3.12.0:
      Successfully uninstalled filelock-3.12.0
  Attempting uninstall: editorconfig-checker
    Found existing installation: editorconfig-checker 2.4.0
    Uninstalling editorconfig-checker-2.4.0:
      Successfully uninstalled editorconfig-checker-2.4.0
  Attempting uninstall: Deprecated
    Found existing installation: Deprecated 1.2.4
    Uninstalling Deprecated-1.2.4:
      Successfully uninstalled Deprecated-1.2.4
  Attempting uninstall: sentry-sdk
    Found existing installation: sentry-sdk 1.9.0
    Uninstalling sentry-sdk-1.9.0:
      Successfully uninstalled sentry-sdk-1.9.0
  Attempting uninstall: requests
    Found existing installation: requests 2.27.1
    Uninstalling requests-2.27.1:
      Successfully uninstalled requests-2.27.1
  Attempting uninstall: platformdirs
    Found existing installation: platformdirs 3.5.0
    Uninstalling platformdirs-3.5.0:
      Successfully uninstalled platformdirs-3.5.0
  Attempting uninstall: elasticsearch
    Found existing installation: elasticsearch 7.1.0
    Uninstalling elasticsearch-7.1.0:
      Successfully uninstalled elasticsearch-7.1.0
  Attempting uninstall: astroid
    Found existing installation: astroid 2.6.6
    Uninstalling astroid-2.6.6:
      Successfully uninstalled astroid-2.6.6
  Attempting uninstall: virtualenv
    Found existing installation: virtualenv 20.23.0
    Uninstalling virtualenv-20.23.0:
      Successfully uninstalled virtualenv-20.23.0
  Attempting uninstall: requests-mock
    Found existing installation: requests-mock 1.9.3
    Uninstalling requests-mock-1.9.3:
      Successfully uninstalled requests-mock-1.9.3
  Attempting uninstall: pylint
    Found existing installation: pylint 2.9.0
    Uninstalling pylint-2.9.0:
      Successfully uninstalled pylint-2.9.0
  Attempting uninstall: pluggy
    Found existing installation: pluggy 0.13.1
    Uninstalling pluggy-0.13.1:
      Successfully uninstalled pluggy-0.13.1
  Attempting uninstall: hvac
    Found existing installation: hvac 0.7.0
    Uninstalling hvac-0.7.0:
      Successfully uninstalled hvac-0.7.0
  Attempting uninstall: click
    Found existing installation: click 7.1.2
    Uninstalling click-7.1.2:
      Successfully uninstalled click-7.1.2
  Attempting uninstall: botocore
    Found existing installation: botocore 1.26.10
    Uninstalling botocore-1.26.10:
      Successfully uninstalled botocore-1.26.10
  Attempting uninstall: tox
    Found existing installation: tox 3.7.0
    Uninstalling tox-3.7.0:
      Successfully uninstalled tox-3.7.0
  Attempting uninstall: s3transfer
    Found existing installation: s3transfer 0.5.2
    Uninstalling s3transfer-0.5.2:
      Successfully uninstalled s3transfer-0.5.2
  Attempting uninstall: pytest
    Found existing installation: pytest 4.6.11
    Uninstalling pytest-4.6.11:
      Successfully uninstalled pytest-4.6.11
  Attempting uninstall: black
    Found existing installation: black 21.10b0
    Uninstalling black-21.10b0:
      Successfully uninstalled black-21.10b0
  Attempting uninstall: pytest-xdist
    Found existing installation: pytest-xdist 1.34.0
    Uninstalling pytest-xdist-1.34.0:
      Successfully uninstalled pytest-xdist-1.34.0
  Attempting uninstall: pytest-mock
    Found existing installation: pytest-mock 3.2.0
    Uninstalling pytest-mock-3.2.0:
      Successfully uninstalled pytest-mock-3.2.0
  Attempting uninstall: pytest-env
    Found existing installation: pytest-env 0.6.2
    Uninstalling pytest-env-0.6.2:
      Successfully uninstalled pytest-env-0.6.2
  Attempting uninstall: pytest-cov
    Found existing installation: pytest-cov 2.5.1
    Uninstalling pytest-cov-2.5.1:
      Successfully uninstalled pytest-cov-2.5.1
  Attempting uninstall: boto3
    Found existing installation: boto3 1.23.4
    Uninstalling boto3-1.23.4:
      Successfully uninstalled boto3-1.23.4
  Attempting uninstall: moto
    Found existing installation: moto 3.1.9
    Uninstalling moto-3.1.9:
      Successfully uninstalled moto-3.1.9
Successfully installed Deprecated-1.2.13 PyYAML-6.0.1 Pygments-2.17.2 Sphinx-5.3.0 alabaster-0.7.13 astroid-2.15.8 azure-core-1.30.1 azure-storage-file-share-12.15.0 babel-2.14.0 black-23.3.0 boto3-1.26.132 botocore-1.29.165 cachetools-5.5.2 chardet-5.2.0 click-8.1.3 colorama-0.4.6 dill-0.3.7 distlib-0.3.9 docutils-0.18.1 editorconfig-checker-2.7.1 elasticsearch-7.17.9 exceptiongroup-1.3.0 filelock-3.12.2 future-0.18.3 hvac-1.1.0 imagesize-1.4.1 iniconfig-2.0.0 isodate-0.7.2 m2r2-0.3.2 mistune-0.8.4 mock-5.0.2 moto-4.1.9 platformdirs-4.0.0 pluggy-1.2.0 psutil-5.9.5 pyhcl-0.4.5 pylint-2.17.4 pyproject-api-1.5.3 pytest-7.3.1 pytest-cov-4.0.0 pytest-env-0.8.1 pytest-mock-3.10.0 pytest-runner-6.0.0 pytest-timeout-2.1.0 pytest-xdist-3.2.1 requests-2.30.0 requests-mock-1.10.0 s3transfer-0.6.2 sentry-sdk-1.22.2 setuptools-67.7.2 six-1.16.0 snowballstemmer-2.2.0 sphinx-rtd-theme-1.2.0 sphinxcontrib-applehelp-1.0.2 sphinxcontrib-devhelp-1.0.2 sphinxcontrib-htmlhelp-2.0.0 sphinxcontrib-jquery-4.1 sphinxcontrib-jsmath-1.0.1 sphinxcontrib-qthelp-1.0.3 sphinxcontrib-serializinghtml-1.1.5 tomli-2.0.1 tomlkit-0.12.5 tox-4.5.1 typing-extensions-4.7.1 urllib3-1.26.20 virtualenv-20.26.3
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
WARNING: There was an error checking the latest version of pip.
[32;1m$ python3 setup.py test[0;m
running pytest
running egg_info
creating elipy2.egg-info
writing elipy2.egg-info/PKG-INFO
writing dependency_links to elipy2.egg-info/dependency_links.txt
writing entry points to elipy2.egg-info/entry_points.txt
writing requirements to elipy2.egg-info/requires.txt
writing top-level names to elipy2.egg-info/top_level.txt
writing manifest file 'elipy2.egg-info/SOURCES.txt'
reading manifest file 'elipy2.egg-info/SOURCES.txt'
writing manifest file 'elipy2.egg-info/SOURCES.txt'
running build_ext
============================= test session starts ==============================
platform linux -- Python 3.7.7, pytest-7.3.1, pluggy-1.2.0 -- /usr/local/bin/python3
cachedir: .pytest_cache
rootdir: /builds/dre-cobra/elipy/elipy2
configfile: setup.cfg
testpaths: elipy2/tests
plugins: requests-mock-1.10.0, xdist-3.2.1, timeout-2.1.0, mock-3.10.0, env-0.8.1, cov-4.0.0, forked-1.6.0
timeout: 2.0s
timeout method: signal
timeout func_only: False
collecting ... 2025-07-07 11:13:24 elipy2 [WARNING]: Got ./elipy2/elipy_example.yml for ELIPY_CONFIG, relative paths must be from %GAME_ROOT%.
2025-07-07 11:13:24 elipy2 [INFO]: Initializing ELIPY2 using config file: /builds/dre-cobra/elipy/elipy2/elipy2/elipy_example.yml
2025-07-07 11:13:24 elipy2 [INFO]: Elipy was installed from AF1.
collected 1771 items / 1 skipped

elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_download_zip_artifact PASSED [  0%]
elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_does_not_download_when_exists PASSED [  0%]
elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_downloads_when_force_download_is_true PASSED [  0%]
elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_returns_correct_path_when_downloading_file PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_get PASSED     [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_put_built_levels PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_delete PASSED  [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_delete_bad_status PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_get_bad_status PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_getdbs PASSED        [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke PASSED          [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_exception PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_exception_not_failing PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_exception_failure_dbs_found PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_timeout PASSED  [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_avalanche PASSED [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_clone_db PASSED      [  0%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_drop PASSED          [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_ddelta_is_path PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_ddelta PASSED        [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export PASSED        [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_noobject_scenario_1 PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_noobject_scenario_2 PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_is_path PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_baselinedb PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_baselinedb_cleanup_stop PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_baselinedb_cleanup_continue PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_importdb PASSED      [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_with_source PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_default PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_no_setting PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_with_setting PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_with_bad_setting PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_complete_clone PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_push_built_levels PASSED [  1%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_new_dest PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_new_dest_extra_args PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_non_default_ordering_algorithm PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_no_ordering_algorithm PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_skip_platform PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_skip_platform_failure PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_create_dir PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_dir_exists PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_combine PASSED       [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_combine_extra_args PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels_with_open PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels_create_path PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels_path_exists PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_ops_chain_with_open PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_ops_chain_exception PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_local_avalanche PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_platform_name PASSED [  2%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_platform_name_reverse PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check_not_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check_no_relevant_process PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check_multiple PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_low_space PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_medium_space PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_high_space PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_running_nodb PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_not_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_ret_failed PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_when_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_when_not_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_api_when_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_api_when_not_running PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_api_when_not_return PASSED [  3%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_restart_avalanche_true PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_restart_avalanche_timeout PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_restart_avalanche_avalanche_already_running PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_stop_avalanche_true PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_stop_avalanche_already_stopped PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_status PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_no_cleanup PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_no_cleanup_too_few_dbs PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_no_cleanup_too_must_space PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_cleanup_avalanche_space PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_cleanup PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_all_data PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_invoke PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_space PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_space_failure PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_space_overflow PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db_valid PASSED [  4%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db_default PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db_valid_default PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name_custom_prefix PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_autotest_temp_db_name PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name_2019_pr7 PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name_2018 PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name PASSED   [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_prefix PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_fb2019_pr7 PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_prefix_fb2019_pr7 PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_fb2019_pr7_server PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_fb2018 PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_prefix_fb2018 PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_exception PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_old_fb_version PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_remote_clone_db PASSED [  5%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_deploy_state_to_bundles PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_success PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_success_with_data PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_already_imported PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_skip_no_new_code PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_skip_reimport_not_needed PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_skip_db_exists PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_old_dbs PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_old_dbs_long_storage_time PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_db_exists PASSED     [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_db_contains PASSED   [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put PASSED     [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put_bad_status PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put_default_header PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put_set_header PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_set_cache_value PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_cache_value PASSED [  6%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_set_cache_value_list PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_cache_value_list PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_reimport_needed_exception PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_drop_build_record PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_empty_dbs PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_drop_all_dbs PASSED  [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_full_database_name PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_short_database_name PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_database_id PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_database_id_not_found PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_fb_branch_id PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalanche::test_get_fb_branch_id_not_found PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_set_avalanche_build_status PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_reimport_needed PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_reimport_needed_db_doesnt_exist PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_reimport_needed_no_data_changelist PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_remote_clone_db PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_maintenance PASSED [  7%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_maintenance_file_not_found PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_maintenance_non_windows PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_upstream_sets PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_upstream_sets_non_windows PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_set_upstream_node PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_set_upstream_node_non_windows PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings_skip_path PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings_skip_size PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings_non_windows PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheAltLocation::test_remote_clone_db_ephemeral_no_setting PASSED [  8%]
elipy2/tests/test_avalanche.py::TestAvalancheAltLocation::test_export_avalanche_state_remote_clone_db_no_setting PASSED [  8%]
elipy2/tests/test_az_utils.py::test_get_azure_fileshare_credentials PASSED [  8%]
elipy2/tests/test_az_utils.py::test_get_offset_datetime PASSED           [  8%]
elipy2/tests/test_az_utils.py::test_get_azure_fileshare_sas_token_called_with_correct_parameters PASSED [  8%]
elipy2/tests/test_az_utils.py::test_yield_azfileshare_path_contents_metadata_is_a_generator PASSED [  8%]
elipy2/tests/test_az_utils.py::test_yield_azfileshare_path_contents_metadata_yields_str_str_dicts PASSED [  8%]
elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_correct_version PASSED [  8%]
elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_not_tested_version PASSED [  9%]
elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_non_default_args PASSED [  9%]
elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_is_not_installed PASSED [  9%]
elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_copy_with_default_args PASSED [  9%]
elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_copy_with_non_default_args PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_all PASSED  [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_call PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_call_failure PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes_NoneAttributes PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes_retry PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_with_query PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_aggregations PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearchMetadataProvider::test_dump_attributes PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilboElasticSearchMetadataProvider::test_dump_attributes_NoneAttributes PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_code_build PASSED   [  9%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles PASSED      [  9%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles_no_path PASSED [  9%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles_non_default_bundle_type PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_set_attribute PASSED         [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_as_used PASSED      [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_no_cl PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_no_branch PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_ant PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_ant_build_no_cl PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_ant_build_no_branch PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols PASSED      [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_avalanche_state PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_state PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_web_export PASSED   [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols_build_no_cl PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols_build_no_branch PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked_data PASSED [ 10%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked_fail PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_build_as_release_candidate PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_drone_build PASSED  [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_with_combine_params PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_with_partial_combine_params PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_data_smoke PASSED   [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_verified_data PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_no_matching_cl PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_method PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_path PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_cl PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test0] PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test1] PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test2] PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test3] PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test4] PASSED [ 11%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_calls__register_build_when_parameters_meet_reqs PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_data_cl PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_code_cl PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_data_branch PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_code_branch PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_dataset PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_platform PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_config PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_pkgtype PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_run_first_time PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_update_test_status PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_category_exists_but_test_has_not_been_created PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_register_new_test_on_existing_test PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_fail PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_handle_race_condition PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_set_autotest_category_status PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_not_set_verified_data_block_no_update_bilbo PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_not_set_branch_changelist_no_update_bilbo PASSED [ 12%]
elipy2/tests/test_bilbo.py::TestBilbo::test_set_autotest_category_status_category_not_found PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_first_time PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_update_is_use_until PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_do_not_update PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_handle_race_condition PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db PASSED     [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_data_cl PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_data_branch PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_code_cl PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_code_branch PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_platform PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_clone_host PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_dest_db PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds PASSED        [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds_query_string_success PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds_query_string_not_found_handled PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_aggregations_success PASSED [ 13%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_aggregations_not_found PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_builds_with_query_success PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_build_by_id_success PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_builds_matching PASSED   [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_last_successful PASSED   [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_last_successful_no PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_get_build_ids PASSED         [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_delete_build PASSED          [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path PASSED        [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path_empth PASSED  [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path_one PASSED    [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_dump_attributes_NoneAttributes PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilbo::test_dump_attributes_lower_case PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilboBuildJsonOverride::test_bilbo_build_json_override PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilboBuildJsonOverride::test_bilbo_build_json_default PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_code_build_exception PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_drone_build_data_changelist_exception PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_drone_build_code_changelist_exception PASSED [ 14%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_no_changelist_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_no_branch_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_data_changelist_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_code_changelist_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_data_branch_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_code_branch_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_dataset_exception PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_data_branch PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_code_branch PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_data_cl PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_code_cl PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_platform PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_set_attribute PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_get_uuid PASSED    [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_get_uuid_uppercase PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_get_uuid_uppercase_and_lowercase_donot_match PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_existing_build PASSED [ 15%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_new_build PASSED [ 16%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_dump_attributes_exception PASSED [ 16%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_build_failures[register_func0] PASSED [ 16%]
elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_build_failures[register_func1] PASSED [ 16%]
elipy2/tests/test_bilbo_es_helper.py::TestBilboElasticSearchHelper::test_get_newest_with_query_string PASSED [ 16%]
elipy2/tests/test_bilbo_es_helper.py::TestBilboElasticSearchHelper::test_get_newest_with_query_string_4_pages PASSED [ 16%]
elipy2/tests/test_bilbo_es_helper.py::TestBilboElasticSearchHelper::test_get_newest_with_query_string_extra_pages_no_hits PASSED [ 16%]
elipy2/tests/test_bilbo_utils.py::TestGetAllBuilds::test_get_all_builds_custom_query PASSED [ 16%]
elipy2/tests/test_build_metadata.py::test_create PASSED                  [ 16%]
elipy2/tests/test_build_metadata.py::test_len PASSED                     [ 16%]
elipy2/tests/test_build_metadata.py::test_delete_build PASSED            [ 16%]
elipy2/tests/test_build_metadata.py::test_get_aggregations PASSED        [ 16%]
elipy2/tests/test_build_metadata.py::test_get_all_builds PASSED          [ 16%]
elipy2/tests/test_build_metadata.py::test_get_all_builds_query_string PASSED [ 16%]
elipy2/tests/test_build_metadata.py::test_get_build_ids PASSED           [ 16%]
elipy2/tests/test_build_metadata.py::test_get_builds_matching PASSED     [ 16%]
elipy2/tests/test_build_metadata.py::test_get_builds_with_query PASSED   [ 16%]
elipy2/tests/test_build_metadata.py::test_get_build_by_id PASSED         [ 16%]
elipy2/tests/test_build_metadata.py::test_get_last_successful PASSED     [ 17%]
elipy2/tests/test_build_metadata.py::test_register_avalanche_state PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_baseline_build PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_build_data PASSED     [ 17%]
elipy2/tests/test_build_metadata.py::test_register_bundles PASSED        [ 17%]
elipy2/tests/test_build_metadata.py::test_register_bundles_alt_type PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_clone_db PASSED       [ 17%]
elipy2/tests/test_build_metadata.py::test_tag_build_as_release_candidate PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_expression_debug_data PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_frosty_build PASSED   [ 17%]
elipy2/tests/test_build_metadata.py::test_register_frosty_build_with_combine_parameters PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_tag_code_build_as_smoked PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_build[register_func0] PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_build[register_func1] PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_register_build[register_func2] PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_set_autotest_category_status PASSED [ 17%]
elipy2/tests/test_build_metadata.py::test_set_attribute PASSED           [ 17%]
elipy2/tests/test_build_metadata.py::test_register_as_used PASSED        [ 18%]
elipy2/tests/test_build_metadata.py::test_register_code_build PASSED     [ 18%]
elipy2/tests/test_build_metadata.py::test_register_tnt_local_build PASSED [ 18%]
elipy2/tests/test_build_metadata.py::test_register_ant_local_build PASSED [ 18%]
elipy2/tests/test_build_metadata.py::test_register_symbols PASSED        [ 18%]
elipy2/tests/test_build_metadata.py::test_register_autotest_state PASSED [ 18%]
elipy2/tests/test_build_metadata.py::test_register_web_export PASSED     [ 18%]
elipy2/tests/test_build_metadata.py::test_register_drone_build PASSED    [ 18%]
elipy2/tests/test_build_metadata.py::test_mark_build_as_in_use_until PASSED [ 18%]
elipy2/tests/test_cli.py::TestCli::test_cli PASSED                       [ 18%]
elipy2/tests/test_cli.py::TestCli::test_cli_without_plugin_folder PASSED [ 18%]
elipy2/tests/test_cli.py::TestCli::test_cli_plugin_folder_duplicate PASSED [ 18%]
elipy2/tests/test_code.py::TestCode::test_constructor_with_exception PASSED [ 18%]
elipy2/tests/test_code.py::TestCode::test_constructor_p4_config_buildsystem_default PASSED [ 18%]
elipy2/tests/test_code.py::TestCode::test_constructor_p4_config_buildsystem_dont_overwrite_p4config PASSED [ 18%]
elipy2/tests/test_code.py::TestCode::test_constructor_p4_config_local PASSED [ 18%]
elipy2/tests/test_code.py::TestCode::test_gensln_fbenv_2018 PASSED       [ 18%]
elipy2/tests/test_code.py::TestCode::test_gensln_fbenv PASSED            [ 18%]
elipy2/tests/test_code.py::TestCode::test_gensln_fbenv_override_config PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_gensln_fbcli PASSED            [ 19%]
elipy2/tests/test_code.py::TestCode::test_gensln_fbcli_wsl PASSED        [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_old PASSED      [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_new PASSED      [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_override_config PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_framework_args PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_fbcli PASSED          [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_notfailonfirst PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_buildsln_tools_fbenv PASSED    [ 19%]
elipy2/tests/test_code.py::TestCode::test_clean_local PASSED             [ 19%]
elipy2/tests/test_code.py::TestCode::test_clean_local_failure PASSED     [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage PASSED           [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_with_framework_args PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_all_config_old_fb PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_debug_config_old_fb PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_release_config_old_fb PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_all_config_new_fb PASSED [ 19%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_debug_config_new_fb PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_release_config_new_fb PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_nantonpackage_with_exception PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_gensln_with_exception PASSED   [ 20%]
elipy2/tests/test_code.py::TestCode::test_buildsln_with_exception PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_buildsln_tool_with_exception PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_xb1 PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_win64 PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_server PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_project_default PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_project_specific PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_fake_platform PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_pc_fail PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_xb1_fail PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_win64 PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_xb1 PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_project_default PASSED [ 20%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_project_specific PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_internal_win64 PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_internal_gen4a PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_increment_client_version_internal_ps4 PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_add_files_to_pushbuild PASSED  [ 21%]
elipy2/tests/test_code.py::TestCode::test_add_files_to_pushbuild_tool PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_add_files_to_pushbuild_no_platform PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_plt PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_platform PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_file_path PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_specified_file_path PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_excluded_file PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_fw_arg PASSED     [ 21%]
elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_platform_as_str PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_platform_as_list PASSED [ 21%]
elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_exception PASSED  [ 21%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_context_multiple_commands PASSED [ 21%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_init PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_read_data_no_file_exists PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_write_and_read_data_no_file_exists PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_start_cmd_log PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_start_cmd_log_task_id PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_start_cmd_log_executor PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_finish_cmd_log PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLogger::test_sig_handler PASSED [ 22%]
elipy2/tests/test_command_logger.py::TestCommandLoggerRunTests::test_run_execute_cmd_file PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_get_setting PASSED   [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_get_setting_invalid_location_exception PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_get_setting_invalid_value_exception PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_get_with_default PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_get_with_default_none PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_load_config PASSED   [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_path_get PASSED      [ 22%]
elipy2/tests/test_config.py::TestConfigModule::test_resolve_setting PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModuleCustomPath::test_get_default_location PASSED [ 22%]
elipy2/tests/test_config.py::TestConfigModuleCustomPath::test_get_production_location PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test__delete_folder_callback_listdir PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test__delete_folder_listdir PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_check_for_handles PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_clean_temp PASSED        [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_clean_temp_exe_allow PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_file_handles PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_file_handles_fail PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_file_handles_no_open PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_api_not_available PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_invalid_path PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_jsondecode_exception_failure PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_jsondecode_exception_success PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_jsondecode_retry_count PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_no_open_handle PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip PASSED        [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip_extra_args PASSED [ 23%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip_failure_wrong_file_extension PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip_makedirs PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip_network_share_path PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip_skip_compression PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_create_zip_skip_compression_network_share_path PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_decode_line_utf16le PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_decode_line_utf8 PASSED  [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_file_fake PASSED  [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_file_real PASSED  [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_filer_folder PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_real PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_real_deeper PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy_already_exists_os_cached PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy_empty PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy_exe PASSED [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_ensure_p4_config PASSED  [ 24%]
elipy2/tests/test_core.py::TestCoreModule::test_ensure_p4_config_None PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_ensure_package_is_installed PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_ensure_package_is_installed_pre2022 PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_ensure_package_is_installed_with_bool PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_extract_zip PASSED       [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_extract_zip_failure_run_7zip PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_extract_zip_failure_wrong_file_extension PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_get_counter_value PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_get_licensee_code_folder PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_get_licensee_code_folder_exception PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_is_buildsystem_run PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_is_filer_api_available PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_list_chunk_generator PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_list_chunk_generator_short_list PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_check PASSED         [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_check_fail PASSED    [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_check_fail_missing PASSED [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder PASSED   [ 25%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_default_chunk_size PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_multiple_folder_levels PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_specified_chunk_size PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_txt PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_undefined_file_type PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_one_file PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_one_file_parallel PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_robocopy PASSED          [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_robocopy_args PASSED     [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_7zip PASSED          [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_7zip_failure PASSED  [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_age_store PASSED     [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_age_store_dryrun PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_async_join PASSED    [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_async_stop_all_mocked PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_run_async_stop_all_normal PASSED [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_update_shell PASSED      [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_use_bilbo PASSED         [ 26%]
elipy2/tests/test_core.py::TestCoreModule::test_write_output PASSED      [ 27%]
elipy2/tests/test_core.py::TestCoreModule::test_write_output_allow_exception PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModule::test_write_output_with_file_io PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModule::test_write_stderr PASSED      [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_check_for_handles_ex PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_create_zip_missing_extension PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_robocopy_exception PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_robocopy_exception_missing_source PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_cmd_types PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_exception_not_list PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_non_zero PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_non_zero_allow PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_non_zero_eapm PASSED [ 27%]
elipy2/tests/test_core.py::TestCoreModuleExceptions::test_use_bilbo_exe PASSED [ 27%]
elipy2/tests/test_core.py::TestPatchEnviron::test_env_changes PASSED     [ 27%]
elipy2/tests/test_core.py::TestPatchEnviron::test_env_reverts PASSED     [ 27%]
elipy2/tests/test_core.py::TestUnderscoreRun::test_variable_expansion PASSED [ 27%]
elipy2/tests/test_core.py::TestUnderscoreRun::test_wait_not_called PASSED [ 28%]
elipy2/tests/test_core.py::TestUnderscoreRun::test_run PASSED            [ 28%]
elipy2/tests/test_core.py::TestGetLogFilePath::test_get_log_file_path_without_env PASSED [ 28%]
elipy2/tests/test_core.py::TestGetBuildTempFolderPath::test_get_build_temp_folder_path PASSED [ 28%]
elipy2/tests/test_core.py::TestGetBuildTempFolderPath::test_get_build_temp_folder_path_jenkins PASSED [ 28%]
elipy2/tests/test_core.py::TestGetLogDirectoryPath::test_get_log_directory_path_local PASSED [ 28%]
elipy2/tests/test_core.py::TestGetLogDirectoryPath::test_get_log_directory_path_ado PASSED [ 28%]
elipy2/tests/test_core.py::TestGetLogDirectoryPath::test_get_log_directory_path_jenkins PASSED [ 28%]
elipy2/tests/test_custom_logging.py::test_ado_logger PASSED              [ 28%]
elipy2/tests/test_custom_logging.py::test_default_logger PASSED          [ 28%]
elipy2/tests/test_custom_logging.py::test_set_level PASSED               [ 28%]
elipy2/tests/test_custom_logging.py::test_logger_start_group PASSED      [ 28%]
elipy2/tests/test_custom_logging.py::test_logger_command PASSED          [ 28%]
elipy2/tests/test_custom_logging.py::test_logger_end_group PASSED        [ 28%]
elipy2/tests/test_custom_logging.py::test_logger_section PASSED          [ 28%]
elipy2/tests/test_custom_logging.py::test_logger_error PASSED            [ 28%]
elipy2/tests/test_data.py::TestData::test_cook_fbenv PASSED              [ 28%]
elipy2/tests/test_data.py::TestData::test_cook_fbcli PASSED              [ 28%]
elipy2/tests/test_data.py::TestData::test_cook_fbcli_skip_indexing PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_fbcli_only_indexing PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_asset_clean_master PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_disable_caches PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_clean_index PASSED        [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_exception PASSED     [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_exception_asset PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_exception_source_layer PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_exception_content_layer PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_exception_and_flag_calls_copy_mdmp_to_filer PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_cook_with_exception_without_flag_doesnt_call_copy_mdmp_to_filer PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer PASSED      [ 29%]
elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_create_dir_if_not_existing PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_dont_create_dir_if_already_existing PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_no_mdmp PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_only_move_mdmp_files PASSED [ 29%]
elipy2/tests/test_data.py::TestData::test_set_datadir PASSED             [ 29%]
elipy2/tests/test_data.py::TestData::test_clean_fbenv PASSED             [ 29%]
elipy2/tests/test_data.py::TestData::test_clean_reimport_without_platform_arg PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_clean_reimport_with_platform_arg PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_clean_extra_pipeline_args PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_clean_ant_local PASSED         [ 30%]
elipy2/tests/test_data.py::TestData::test_clean_ant_local_no_file PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_clean_ant_local_dont_recreate_file_if_existing PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_clean_local_datastate PASSED   [ 30%]
elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata_with_clean_args PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata_fail_not_bct PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata_fail_bct PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_run_guid_checker PASSED        [ 30%]
elipy2/tests/test_data.py::TestData::test_run_guid_checker_non_default_guid_checker_dir PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_run_guid_checker_non_default_check_dir PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_run_guid_checker_failure PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_run_frostbite_data_dupgrade PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_run_frostbite_data_upgrade_default PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_run_frostbite_data_upgrade_extra_args PASSED [ 30%]
elipy2/tests/test_data.py::TestData::test_get_clean_master_version_args PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_clear_cache_no_args PASSED     [ 31%]
elipy2/tests/test_data.py::TestData::test_clear_cache_extra_args PASSED  [ 31%]
elipy2/tests/test_data.py::TestData::test_cook_response_file PASSED      [ 31%]
elipy2/tests/test_data.py::TestData::test_cook_few_assets PASSED         [ 31%]
elipy2/tests/test_data.py::TestData::test_cook_few_assets_content_layer PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_cook_many_assets PASSED        [ 31%]
elipy2/tests/test_data.py::TestData::test_response_cooking PASSED        [ 31%]
elipy2/tests/test_data.py::TestData::test_response_file_exists_on_failure PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_chunk_cooking_few_assets PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_chunk_cooking_many_assets PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_pipeline_args_default PASSED   [ 31%]
elipy2/tests/test_data.py::TestData::test_pipeline_args_include_arg PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_skip_trim PASSED               [ 31%]
elipy2/tests/test_data.py::TestData::test_pipeline_args_trim_already_included PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_data_get_content_layer_empty PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_data_get_content_layer_not_set PASSED [ 31%]
elipy2/tests/test_data.py::TestData::test_data_get_content_layer_missing PASSED [ 32%]
elipy2/tests/test_data.py::TestData::test_data_get_content_layer_end_case_insensitive PASSED [ 32%]
elipy2/tests/test_data.py::TestData::test_data_get_content_layer_middle PASSED [ 32%]
elipy2/tests/test_data.py::TestDataInit::test_data_init_local PASSED     [ 32%]
elipy2/tests/test_data.py::TestDataInit::test_data_init_buildfarm_default PASSED [ 32%]
elipy2/tests/test_data.py::TestDataInit::test_data_init_buildfarm_monkey_build_label PASSED [ 32%]
elipy2/tests/test_data.py::TestDataInit::test_data_init_buildfarm_skip_overwrite_p4config PASSED [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap PASSED                [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap_binary_path PASSED    [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_get_denuvo_program_args_no_quotes PASSED [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_get_denuvo_program_args_relative_exclusions_path PASSED [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_get_denuvo_program_args_absolute_exclusions_path PASSED [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test__get_version_specific_args_exclusions_path_does_not_exist PASSED [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap_denuvo_failed PASSED  [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_get_tnt_root PASSED        [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test__get_file_paths PASSED     [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test__get_file_paths_file_in_unexpected_directory PASSED [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_fetch_denuvo_files PASSED  [ 32%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_fetch_denuvo_files_rerun PASSED [ 33%]
elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap_v2 PASSED             [ 33%]
elipy2/tests/test_denuvo.py::TestDenuvo::test__get_file_paths_v2 PASSED  [ 33%]
elipy2/tests/test_enum_utils.py::TestEnumUtils::test_string_to_enum PASSED [ 33%]
elipy2/tests/test_enum_utils.py::TestEnumUtils::test_string_to_enum_case_insenstive PASSED [ 33%]
elipy2/tests/test_enum_utils.py::TestEnumUtils::test_string_to_enum_not_member PASSED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_dry_run PASSED      [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_onefs_api PASSED    [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire PASSED              [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception FAILED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception FAILED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_handles_exceptions_from_delete_with_onefs_api PASSED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_handles_exceptions_from_delete_filer_folder PASSED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_response_run PASSED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_response_run_invalid PASSED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_expire_response_dry_run PASSED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire FAILED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire_multi FAILED [ 33%]
elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_with_onfs_api PASSED [ 34%]
elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path PASSED      [ 34%]
elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_keep_all PASSED [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_run PASSED                   [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_gensln PASSED                [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_gensln_wsl PASSED            [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_gensln_framework_args PASSED [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_buildsln PASSED              [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_buildsln_msbuild_args PASSED [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_nant PASSED                  [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_cook PASSED                  [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_cook_multiple_assets PASSED  [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_icepick_cook PASSED          [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_icepick_cook_multiple_assets PASSED [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild PASSED             [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild_path_subpath_multiple_platforms PASSED [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild_invalid_args PASSED [ 34%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild_eacopy_args PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pullbuild PASSED             [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pullbuild_false_default_args PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_pullbuild_path_subpath PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_run_no_method_args PASSED    [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_enable_licensee PASSED       [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_disable_licensee PASSED      [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_switch_licensee PASSED       [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_view_licensee PASSED         [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_set_licensee_undefined_action PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_run_no_method PASSED         [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_append_fbcli_arg_condition_True PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_append_fbcli_arg_condition_False PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_append_fbcli_arg_condition_None_Value PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_install_prerequisites_default PASSED [ 35%]
elipy2/tests/test_fbcli.py::TestFbcli::test_install_prerequisites_custom PASSED [ 35%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_platform_data PASSED [ 35%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_platform_data_exception PASSED [ 35%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_normalize_platform PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_normalize_platform_exception PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_binary_internal PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_binary_api PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_binary_exception PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_directory_internal PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_directory_api PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_directory_exception PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_frosted_directory_internal PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_frosted_directory_api PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_frosted_directory_exception PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_pipeline_directory_internal PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_pipeline_directory_api PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_pipeline_directory_exception PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_framework_args PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_framework_args_list_of_platforms PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_framework_args_tuple_of_platforms PASSED [ 36%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_failure_no_platform PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_failure_no_input_file PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_failure_fbenv PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_only_platforms PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_only_platforms PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_only_pipelineargs PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_only_pipelineargs PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_platforms_assets PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_platforms_assets PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_platforms_pipelineargs PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_platforms_pipelineargs PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_all_params PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_all_params_new_fb PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_index PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_index_deprecated PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_all_params PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_not_local PASSED [ 37%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_not_local PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_attach PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_attach PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_without_params_fail PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_only_assets_fail PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_assets_pipelineargs_fail PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbenv_exception PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_utility_path_exists_true PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_utility_path_exists_false PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_before_2022 PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_early_2022 PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_no_utility_file PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_with_utility_file PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_no_utility_file_exception PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_with_utility_file_exception PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_2022 PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_pre_2022 PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_2022_failure PASSED [ 38%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_pre_2022_failure PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_default PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_extra_args PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_not_use_local PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_not_ignore_lock PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_mirror_skip PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_mirror_use PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_default PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_local PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_not_use_local PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_exception PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_mirror_skip PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_mirror_use PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_default PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_extra_args PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_mirror_skip PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_mirror_use PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_default PASSED [ 39%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_local PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_not_use_local PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_exception PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_mirror_skip PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_mirror_use PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_only_platforms PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_all_params_no_bespoke PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_failure PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_with_bespoke_flag PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_bespoke_frosty PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_bespoke_frosty_region PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_bespoke_frosty_args_to_remove PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbenv_gensln_with_stressbulkbuild_arg PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbenv_gensln_without_stressbulkbuild_arg PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbcli_gensln_with_stressbulkbuild_arg PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbcli_gensln_without_stressbulkbuild_arg PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_with_stressbulkbuild_arg PASSED [ 40%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_without_stressbulkbuild_arg PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_with_alltests_and_minimum_fb_version PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_with_wsl_and_not_minimum_fb_version PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_variants PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_with_alltests PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_with_wsl PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbcli PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv_fail_on_first PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv_not_fail_on_first PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv_framework_args PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_datadir PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_datadir_fbcli PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_icepick_cook_fbcli PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_icepick_cook_fbenv_settings_not_found PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_initialize PASSED [ 41%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_enabled_licensees_nant PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_enabled_licensees_fbenv PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_local_root PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_local_root_exception PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_is_api_function_failed_exception_false PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_is_api_function_failed_exception_true PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_environment_values_api PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_environment_values_exception PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_install_prerequisites_default PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_install_prerequisites_custom PASSED [ 42%]
elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_install_prerequisites_fbcli PASSED [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code PASSED           [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_skip_bilbo PASSED [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_no_bilbo PASSED  [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_local PASSED     [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_already_deployed PASSED [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_custom_source PASSED [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_with_tnt PASSED  [ 42%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_with_tnt_overwrite PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_tool PASSED      [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_tnt PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_with_tnt PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_tool PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_custom_binaries_destination PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_tool_tool_targets_override PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_platform_tool_targets_override PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_no_licensee PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_multi_licensee PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_single_licensee PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_not_use_fbcli_custom_tag PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_custom_tag PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_mirror_false PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_artifact_override PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_no_licensee PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_multi_licensee PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_single_licensee PASSED [ 43%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_not_use_fbcli_custom_tag PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_custom_tag PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_artifact_override PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_tool_deploy_frostedtests PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_tnt_use_fbenv_copy PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args_with_tests PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args_overwrite_check PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_file_issue PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_deploy_tests_file_issue PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_zip PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_nomaster PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_nobilbo PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_no_cl PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_not_there PASSED [ 44%]
elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_http_error PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build_no_bilbo PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build_exception PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_nomaster PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_zip PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_zip_failure PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_failure PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_plt PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_plt_no_cl PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_no_dir PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_state PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_state_no_bilbo PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_no_dir PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_no_bilbo PASSED [ 45%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_use_bilbo PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code PASSED            [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_no_bilbo PASSED   [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_artifact_fb2019_no_bilbo PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo_fbenv_args PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo_fetch_tests PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_tool PASSED       [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_custom_destination PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_with_unknown_platform PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_code_not_use_bilbo PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build PASSED    [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_dest PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_by_source PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_by_source_dest PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_create_combine_stream_info_file PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_create_combine_stream_info_file_deployed PASSED [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build PASSED   [ 46%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_no_bilbo PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_custom_source PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_no_source_specified PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_dest_exists_exception PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_combined PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_with_combine_params PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles PASSED  [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles_non_default_dir_name PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles_deployed PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles PASSED   [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles_non_default_dir_name PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles_deployed PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output_non_default_dir_name PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output_deployed PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_state PASSED          [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_state_non_default_bundle_dir_name PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_deploy_state_deployed PASSED [ 47%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state PASSED  [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state_non_default_dir_name PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_delta_bundles PASSED   [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_delta_bundles_no_dest PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_head_bundles PASSED    [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_head_bundles_non_default_dir_name PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state_no_dest PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_dest PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_non_default_dir_name PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_dest_non_default_dir_name PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_no_dest PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_non_default_dir_name PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_no_dest_non_default_dir_name PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest_failure_no_destination PASSED [ 48%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest_combine PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_baseline_xb1_layout PASSED   [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_baseline_xb1_layout_no_dest_exception PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_old PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_new PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_no_dest_exception PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_wrong_platform PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx_exception_file_not_found PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx_combine PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_baseline_ps4_package PASSED  [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_baseline_ps_package PASSED   [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_baseline_ps5_package PASSED  [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_baseline_ps5_package_combine PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_on_success PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_key_error PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_failed_validation PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_failed_deletion PASSED [ 49%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_path_doesnt_exist PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_path_exist_but_is_broken PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_usrpass PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_usrpass_none_found PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_get_case_sensitive_path_success PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_get_case_sensitive_path_failed PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_cancel_network_connection_all PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_cancel_network_connection_with_argument PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_variable PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_undefined_network PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_vault PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_no_cred PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_cred_from_ess PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_no_creds_found_in_ess PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_wrong_creds_keys_in_ess PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_bad_format_found_in_ess PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_exception PASSED [ 50%]
elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_no_drive PASSED [ 50%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_build_share_path_buildsystem PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_build_share_path_user PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_build_share_path_no_user PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path_nomaster PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path_stressbulkbuild PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path_custom_tag PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_add_build_share_user_postfix_noautobuild PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_add_build_share_user_postfix_noautobuild_no_user PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path_custom_tag PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_platform_path PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_delta_bundles_path PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_delta_bundles_path_non_default_bundles_dir_name PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_head_bundles_path PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_head_bundles_path_non_default_bundles_dir_name PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_symbol_path PASSED [ 51%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_symbol_path_win64 PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_delta_bundles_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_delta_bundles_path_non_default_bundles_dir_name PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_state_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_state_path_non_default_dir_name PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_state_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_build_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_build_path_non_default_location PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_head_bundles_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_head_bundles_path_non_default_bundles_dir_name PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_build_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_build_path_combine PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_build_path_with_content_layer PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_base_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_build_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_drone_build_path PASSED [ 52%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_build PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_drone PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_outsourcer_build_location PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_outsourcer_build PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_tnt_local_path PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_tnt_local_path_nomaster PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_tnt_local_path_stressbulkbuild PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_avalanche_export_path PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_expression_debug_data_path PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_binlog_build_path_meta PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_binlog_build_path_non_meta PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_ant_local_build_path PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_shift_delivery_path_code PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_shift_delivery_path_other_build_type PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[None-bfglacier-default location alternate build share bfglacier] PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[default-bfglacier-default location alternate build share bfglacier] PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[criterion-None-criterion location build share] PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[criterion-nfsmerlin-criterion location alternate build share nfsmerlin] PASSED [ 53%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[default-mirrorsedge-default location alternate build share mirrorsedge] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[criterion-nfsexcalibur-criterion location alternate build share nfsexcalibur] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path_with_target_build_share[game-dev-1337-plt-final-None-None-expected0] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path_with_target_build_share[game-dev-1337-plt-final-None-alt_buildshare-expected1] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-None-None] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-None-alt_buildshare] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-criterion-None] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-dice-alt_buildshare] PASSED [ 54%]
elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_shift_processing_target_path PASSED [ 54%]
elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_generic_threshold_clean_called_with_correct_arguments PASSED [ 54%]
elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_generic_threshold_clean_called_with_all_arguments PASSED [ 54%]
elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_generic_threshold_clean_cleaup_not_called_when_invalid_workspace PASSED [ 54%]
elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_get_cleanup_script_path PASSED [ 54%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_game_root PASSED [ 54%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_branch_id PASSED [ 54%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_tnt_root PASSED [ 54%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_licensee_code_folder PASSED [ 54%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_licensee_id PASSED [ 54%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_game_data_dir PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_minimum_fb_version PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_minimum_fb_version_except PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_read_fb_version PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_set_monkey_build_label PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModule::test_set_monkey_build_label_default PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_tnt_root_exception PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_licensee_id_exception PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_game_data_dir_exception PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_game_root_exception PASSED [ 55%]
elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_branch_id_exception PASSED [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fbcli PASSED [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fbcli_args PASSED [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick PASSED       [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fbcli_args_elipy_config_args PASSED [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_with_config PASSED [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_with_exception PASSED [ 55%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fail PASSED  [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_ps4 PASSED   [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_ps5 PASSED   [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_test_suite PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook PASSED  [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_hailstorm PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_hailstorm_disabled PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_fbcli PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_fbcli_default_extra_framework_args PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_override_settings_empty PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_exception PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_raise_exception PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_ignore_exception PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_format_settings_files PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_clean_local_frosty PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_clean_icepicktemp PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_get_extra_test_suite_args_basic PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_get_extra_test_suite_args_test_group PASSED [ 56%]
elipy2/tests/test_icepick.py::TestIcepick::test_get_extra_test_suite_args_custom_test_suite_data PASSED [ 57%]
elipy2/tests/test_icepick.py::TestIcepick::test_get_fb_icepick_args_basic PASSED [ 57%]
elipy2/tests/test_icepick.py::TestIcepick::test_minimum_args PASSED      [ 57%]
elipy2/tests/test_icepick.py::TestIcepick::test_get_fb_icepick_args_all PASSED [ 57%]
elipy2/tests/test_init.py::TestSetCodeArea::test_set_code_area PASSED    [ 57%]
elipy2/tests/test_init.py::TestSetCodeArea::test_handles_exceptions PASSED [ 57%]
elipy2/tests/test_init.py::TestSetCodeArea::test_fbenv_stacktrace PASSED [ 57%]
elipy2/tests/test_init.py::TestDropUnwantedEvents::test_drop_unwanted_events_drop_event PASSED [ 57%]
elipy2/tests/test_init.py::TestDropUnwantedEvents::test_drop_unwanted_events PASSED [ 57%]
elipy2/tests/test_init.py::TestBeforeSend::test_normal_event PASSED      [ 57%]
elipy2/tests/test_init.py::TestBeforeSend::test_fbenv_event_dropped PASSED [ 57%]
elipy2/tests/test_init.py::TestIsDryRun::test_is_dry_run PASSED          [ 57%]
elipy2/tests/test_init.py::TestSentrySetup::test_frostbite_variables[os_env_patch0] PASSED [ 57%]
elipy2/tests/test_init.py::TestSentrySetup::test_frostbite_variables[os_env_patch1] PASSED [ 57%]
elipy2/tests/test_init.py::TestSentrySetup::test_enable_variable[os_env_patch0] PASSED [ 57%]
elipy2/tests/test_init.py::TestSentrySetup::test_enable_variable[os_env_patch1] PASSED [ 57%]
elipy2/tests/test_init.py::TestSentrySetup::test_ado_tags PASSED         [ 57%]
elipy2/tests/test_init.py::TestSentrySetup::test_jenkins_tags PASSED     [ 57%]
elipy2/tests/test_init.py::TestLogging::test_str_to_log_level PASSED     [ 58%]
elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af2 PASSED [ 58%]
elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af1_no_user PASSED [ 58%]
elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af1_no_token PASSED [ 58%]
elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af1_none_set PASSED [ 58%]
elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_get_enabled_licensee_names_called PASSED [ 58%]
elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_nant_get_enabled_licensee_names_called PASSED [ 58%]
elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_set_licensee_code_config_called PASSED [ 58%]
elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_nant_generate_licensee_fragment_info_called PASSED [ 58%]
elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_set_nant_cached_licensee_dict PASSED [ 58%]
elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_licensee_update_enables_called PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_ant_path PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_ant_local_path PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_ant_path_fail PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_antifreeze_dir PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_fdu_dir PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path PASSED [ 58%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_dll PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_no_config PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_unknown_platform PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_artifact_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_bundles_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_bundles_path_non_default PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_scripts_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_frosty_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_licensee_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_avalanchecli_exe_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_avalanchecli_exe_path_exception PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_avalanche_export_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_binary_path PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_binary_path_not_defined PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_binary_path_fbenv PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_executable_name_pattern PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_non_fbenv PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_general PASSED [ 59%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_ant PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_tool PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_frosted PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_pipeline PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_non_win64 PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_win64 PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_win64_trial PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_tool_x3 PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_win64_no_config PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_non_win64_no_config PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_no_platform PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_expressiondebug_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_drive_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_packages_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_packagesdev_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_pip_cache_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_logs_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_dataset_state_path PASSED [ 60%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_tnt_localpackages_path PASSED [ 61%]
elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_tnt_packages_path PASSED [ 61%]
elipy2/tests/test_multiprocessing.py::test_func PASSED                   [ 61%]
elipy2/tests/test_multiprocessing.py::TestCoreModule::test_run_map PASSED [ 61%]
elipy2/tests/test_multiprocessing.py::TestCoreModule::test_run_starmap PASSED [ 61%]
elipy2/tests/test_oreans.py::TestOreans::test_protect PASSED             [ 61%]
elipy2/tests/test_oreans.py::TestOreans::test__get_oreans_program_args PASSED [ 61%]
elipy2/tests/test_oreans.py::TestOreans::test_fetch_oreans_files PASSED  [ 61%]
elipy2/tests/test_p4.py::TestP4::test__p4 PASSED                         [ 61%]
elipy2/tests/test_p4.py::TestP4::test__p4_return_value PASSED            [ 61%]
elipy2/tests/test_p4.py::TestP4::test__switch PASSED                     [ 61%]
elipy2/tests/test_p4.py::TestP4::test_change_dont_discard PASSED         [ 61%]
elipy2/tests/test_p4.py::TestP4::test_change_full PASSED                 [ 61%]
elipy2/tests/test_p4.py::TestP4::test_changes_range_keep_start PASSED    [ 61%]
elipy2/tests/test_p4.py::TestP4::test_changes_range_start_not_in_range PASSED [ 61%]
elipy2/tests/test_p4.py::TestP4::test_changes_range_with_end PASSED      [ 61%]
elipy2/tests/test_p4.py::TestP4::test_changes_range_with_head PASSED     [ 61%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags PASSED              [ 61%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags_byte_input PASSED   [ 62%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags_empty_p4_response PASSED [ 62%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags_false PASSED        [ 62%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags_missing_tags PASSED [ 62%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags_multiple_changelists PASSED [ 62%]
elipy2/tests/test_p4.py::TestP4::test_check_for_tags_no_match PASSED     [ 62%]
elipy2/tests/test_p4.py::TestP4::test_clean PASSED                       [ 62%]
elipy2/tests/test_p4.py::TestP4::test_clean_folder PASSED                [ 62%]
elipy2/tests/test_p4.py::TestP4::test_clean_no_data PASSED               [ 62%]
elipy2/tests/test_p4.py::TestP4::test_clean_no_keys PASSED               [ 62%]
elipy2/tests/test_p4.py::TestP4::test_clean_perforce PASSED              [ 62%]
elipy2/tests/test_p4.py::TestP4::test_clean_quiet PASSED                 [ 62%]
elipy2/tests/test_p4.py::TestP4::test_copy PASSED                        [ 62%]
elipy2/tests/test_p4.py::TestP4::test_copy_args PASSED                   [ 62%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping PASSED                [ 62%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping_args PASSED           [ 62%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping_data_exec PASSED      [ 62%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping_data_not_merges PASSED [ 63%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping_data_valid PASSED     [ 63%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping_must_sync PASSED      [ 63%]
elipy2/tests/test_p4.py::TestP4::test_copy_mapping_nodata PASSED         [ 63%]
elipy2/tests/test_p4.py::TestP4::test_delete_workspace PASSED            [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_fetch PASSED           [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_generic_failure PASSED [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_no_code PASSED         [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_non_failing_error PASSED [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_non_failing_error_quiet PASSED [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_push PASSED            [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_quiet PASSED           [ 63%]
elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_remote_spec_failure PASSED [ 63%]
elipy2/tests/test_p4.py::TestP4::test_edit PASSED                        [ 63%]
elipy2/tests/test_p4.py::TestP4::test_edit_failure PASSED                [ 63%]
elipy2/tests/test_p4.py::TestP4::test_edit_integrated_files PASSED       [ 63%]
elipy2/tests/test_p4.py::TestP4::test_edit_integrated_files_none_found PASSED [ 63%]
elipy2/tests/test_p4.py::TestP4::test_fetch_basic PASSED                 [ 63%]
elipy2/tests/test_p4.py::TestP4::test_fetch_dry_run PASSED               [ 64%]
elipy2/tests/test_p4.py::TestP4::test_fetch_dvcs_errors PASSED           [ 64%]
elipy2/tests/test_p4.py::TestP4::test_fetch_extra_args PASSED            [ 64%]
elipy2/tests/test_p4.py::TestP4::test_fetch_quiet PASSED                 [ 64%]
elipy2/tests/test_p4.py::TestP4::test_fetch_verbose PASSED               [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags PASSED              [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_empty_p4_response PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_false PASSED        [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_missing_tags PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_multiple_changelists PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_cl PASSED          [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_empty_p4_response PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_missing_desc_and_data PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_multiple_cl PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_no_access PASSED   [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_no_access_2 PASSED [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_no_cl PASSED       [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_no_valid PASSED    [ 64%]
elipy2/tests/test_p4.py::TestP4::test_get_description_other_data PASSED  [ 65%]
elipy2/tests/test_p4.py::TestP4::test_getcounter PASSED                  [ 65%]
elipy2/tests/test_p4.py::TestP4::test_getcounter_none_found PASSED       [ 65%]
elipy2/tests/test_p4.py::TestP4::test_ignores PASSED                     [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate PASSED                   [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_bad_resolve_mode PASSED  [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_datavalid PASSED         [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_exception_invalid_option PASSED [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_exception_not_in_client_view PASSED [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_exception_usage PASSED   [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_exception_wrong_arguments PASSED [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_exe PASSED               [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_exe_too_many_rows_scanned PASSED [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_ignore_source_history PASSED [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_no_data PASSED           [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_no_mapping PASSED        [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_resolve_mode_lst PASSED  [ 65%]
elipy2/tests/test_p4.py::TestP4::test_integrate_resolve_mode_str PASSED  [ 66%]
elipy2/tests/test_p4.py::TestP4::test_integrate_reverse PASSED           [ 66%]
elipy2/tests/test_p4.py::TestP4::test_integrate_stream_with_parent PASSED [ 66%]
elipy2/tests/test_p4.py::TestP4::test_integrate_use_file_paths PASSED    [ 66%]
elipy2/tests/test_p4.py::TestP4::test_integrate_use_file_paths_with_revision PASSED [ 66%]
elipy2/tests/test_p4.py::TestP4::test_interchanges_cl PASSED             [ 66%]
elipy2/tests/test_p4.py::TestP4::test_interchanges_no_change PASSED      [ 66%]
elipy2/tests/test_p4.py::TestP4::test_interchanges_no_valid PASSED       [ 66%]
elipy2/tests/test_p4.py::TestP4::test_merge PASSED                       [ 66%]
elipy2/tests/test_p4.py::TestP4::test_merge_both_data_correct PASSED     [ 66%]
elipy2/tests/test_p4.py::TestP4::test_merge_both_exe PASSED              [ 66%]
elipy2/tests/test_p4.py::TestP4::test_merge_no_data PASSED               [ 66%]
elipy2/tests/test_p4.py::TestP4::test_merge_reverse PASSED               [ 66%]
elipy2/tests/test_p4.py::TestP4::test_opened PASSED                      [ 66%]
elipy2/tests/test_p4.py::TestP4::test_opened_empty_response PASSED       [ 66%]
elipy2/tests/test_p4.py::TestP4::test_opened_unexpected_response PASSED  [ 66%]
elipy2/tests/test_p4.py::TestP4::test_p4set PASSED                       [ 66%]
elipy2/tests/test_p4.py::TestP4::test_push_basic PASSED                  [ 66%]
elipy2/tests/test_p4.py::TestP4::test_push_dry_run PASSED                [ 67%]
elipy2/tests/test_p4.py::TestP4::test_push_dvcs_errors PASSED            [ 67%]
elipy2/tests/test_p4.py::TestP4::test_push_extra_args PASSED             [ 67%]
elipy2/tests/test_p4.py::TestP4::test_push_quiet PASSED                  [ 67%]
elipy2/tests/test_p4.py::TestP4::test_push_verbose PASSED                [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reconcile_fail PASSED              [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reconcile_post_result PASSED       [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reconcile_quiet PASSED             [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reconcile_success1 PASSED          [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reconcile_success2 PASSED          [ 67%]
elipy2/tests/test_p4.py::TestP4::test_remotes_log PASSED                 [ 67%]
elipy2/tests/test_p4.py::TestP4::test_remotes_no_data_returned PASSED    [ 67%]
elipy2/tests/test_p4.py::TestP4::test_remotes_not_force_case_insensitive PASSED [ 67%]
elipy2/tests/test_p4.py::TestP4::test_remotes_quiet PASSED               [ 67%]
elipy2/tests/test_p4.py::TestP4::test_remotes_with_data PASSED           [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reopen PASSED                      [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reshelve PASSED                    [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reshelve_no_pending_changelist PASSED [ 67%]
elipy2/tests/test_p4.py::TestP4::test_reshelve_pending_changelist PASSED [ 68%]
elipy2/tests/test_p4.py::TestP4::test_reshelve_with_all PASSED           [ 68%]
elipy2/tests/test_p4.py::TestP4::test_reshelve_with_target PASSED        [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve PASSED                     [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_failed PASSED              [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_failed2 PASSED             [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_mode PASSED                [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_mode_invalid PASSED        [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_nodata PASSED              [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_one PASSED                 [ 68%]
elipy2/tests/test_p4.py::TestP4::test_resolve_path PASSED                [ 68%]
elipy2/tests/test_p4.py::TestP4::test_revert PASSED                      [ 68%]
elipy2/tests/test_p4.py::TestP4::test_revert_no_data_in_return PASSED    [ 68%]
elipy2/tests/test_p4.py::TestP4::test_revert_quiet PASSED                [ 68%]
elipy2/tests/test_p4.py::TestP4::test_revert_return_data PASSED          [ 68%]
elipy2/tests/test_p4.py::TestP4::test_revert_with_wipe_and_only_changed_true PASSED [ 68%]
elipy2/tests/test_p4.py::TestP4::test_set_description PASSED             [ 68%]
elipy2/tests/test_p4.py::TestP4::test_set_description_code_error PASSED  [ 69%]
elipy2/tests/test_p4.py::TestP4::test_set_description_code_info PASSED   [ 69%]
elipy2/tests/test_p4.py::TestP4::test_set_description_code_stat PASSED   [ 69%]
elipy2/tests/test_p4.py::TestP4::test_set_description_no_code PASSED     [ 69%]
elipy2/tests/test_p4.py::TestP4::test_set_description_no_description_returned PASSED [ 69%]
elipy2/tests/test_p4.py::TestP4::test_set_environment PASSED             [ 69%]
elipy2/tests/test_p4.py::TestP4::test_set_environment_no_values PASSED   [ 69%]
elipy2/tests/test_p4.py::TestP4::test_setcounter PASSED                  [ 69%]
elipy2/tests/test_p4.py::TestP4::test_setcounter_no_value PASSED         [ 69%]
elipy2/tests/test_p4.py::TestP4::test_setcounter_wrong_return_value PASSED [ 69%]
elipy2/tests/test_p4.py::TestP4::test_shelve_dont_discard PASSED         [ 69%]
elipy2/tests/test_p4.py::TestP4::test_shelve_dont_force PASSED           [ 69%]
elipy2/tests/test_p4.py::TestP4::test_shelve_full PASSED                 [ 69%]
elipy2/tests/test_p4.py::TestP4::test_stream_info PASSED                 [ 69%]
elipy2/tests/test_p4.py::TestP4::test_stream_info_doesnt_exist PASSED    [ 69%]
elipy2/tests/test_p4.py::TestP4::test_submit PASSED                      [ 69%]
elipy2/tests/test_p4.py::TestP4::test_submit_call PASSED                 [ 69%]
elipy2/tests/test_p4.py::TestP4::test_submit_call_revert_unchanged PASSED [ 69%]
elipy2/tests/test_p4.py::TestP4::test_submit_empty PASSED                [ 70%]
elipy2/tests/test_p4.py::TestP4::test_submit_exception PASSED            [ 70%]
elipy2/tests/test_p4.py::TestP4::test_submit_nofiles PASSED              [ 70%]
elipy2/tests/test_p4.py::TestP4::test_submit_nofiles_v2 PASSED           [ 70%]
elipy2/tests/test_p4.py::TestP4::test_switch PASSED                      [ 70%]
elipy2/tests/test_p4.py::TestP4::test_sync PASSED                        [ 70%]
elipy2/tests/test_p4.py::TestP4::test_sync_error_failure PASSED          [ 70%]
elipy2/tests/test_p4.py::TestP4::test_sync_error_ignore PASSED           [ 70%]
elipy2/tests/test_p4.py::TestP4::test_sync_to_revision PASSED            [ 70%]
elipy2/tests/test_p4.py::TestP4::test_sync_write_to_logfile PASSED       [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files PASSED       [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_no_resolve_type_for_file PASSED [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_no_resolve_type_in_input PASSED [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_other_resolve_type PASSED [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_some_missing_local PASSED [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_without_files PASSED    [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unresolved_without_files_error_data_missing PASSED [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unshelve PASSED                    [ 70%]
elipy2/tests/test_p4.py::TestP4::test_unshelve_code_missing_in_response_item PASSED [ 71%]
elipy2/tests/test_p4.py::TestP4::test_unshelve_exception PASSED          [ 71%]
elipy2/tests/test_p4.py::TestP4::test_unshelve_no_force PASSED           [ 71%]
elipy2/tests/test_p4.py::TestP4::test_unshelve_none PASSED               [ 71%]
elipy2/tests/test_p4.py::TestP4::test_unshelve_override_default_cl PASSED [ 71%]
elipy2/tests/test_p4.py::TestP4::test_unshelve_success PASSED            [ 71%]
elipy2/tests/test_p4.py::TestP4::test_wipe_client PASSED                 [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_describe PASSED               [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_describe_no_changelist PASSED [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_describe_shelved PASSED       [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_describe_show_shelved_files PASSED [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_p4_executable_exists PASSED   [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_p4_executable_does_not_exist PASSED [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_p4_valueerror PASSED          [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_p4_filenotfounderror PASSED   [ 71%]
elipy2/tests/test_p4.py::TestP4Other::test_get_client PASSED             [ 71%]
elipy2/tests/test_package.py::TestPackage::test__supported PASSED        [ 71%]
elipy2/tests/test_package.py::TestPackage::test_frosty PASSED            [ 71%]
elipy2/tests/test_package.py::TestPackage::test_frosty_with_params PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_frosty_with_keyerror PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_frosty_with_exception PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_xbsx PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_exists_xb1 PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_exists_xbsx PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_fetch_appversion PASSED  [ 72%]
elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_double PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_double_digit PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_no_files PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_no_path PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_fetch_contversion PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test__fetch_version PASSED    [ 72%]
elipy2/tests/test_package.py::TestPackage::test_build_system_init PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_get_disc_package_type_both PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_get_disc_package_type_digital PASSED [ 72%]
elipy2/tests/test_package.py::TestPackage::test_get_disc_package_type_none PASSED [ 73%]
elipy2/tests/test_package_utils.py::TestPackageUtils::test_package_downloads_called PASSED [ 73%]
elipy2/tests/test_package_utils.py::TestPackageUtils::test_find_package_called PASSED [ 73%]
elipy2/tests/test_package_utils.py::TestPackageUtils::test_nant_on_package_called PASSED [ 73%]
elipy2/tests/test_package_utils.py::TestPackageUtils::test_nant_on_package_exception PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock==5.0.2-expected0] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock!=5.0.2-expected1] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock<=5.0.2-expected2] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock>=5.0.2-expected3] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock>5.0.2-expected4] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock<5.0.2-expected5] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock~=5.0.2-expected6] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock===5.0.2-expected7] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs0-setup_py_reqs0-expected0] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs1-setup_py_reqs1-expected1] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs2-setup_py_reqs2-expected2] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs3-setup_py_reqs3-expected3] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs4-setup_py_reqs4-expected4] PASSED [ 73%]
elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs5-setup_py_reqs5-expected5] PASSED [ 74%]
elipy2/tests/test_requirements.py::test_elipy2_requirements_txt_aligned_with_elipy2_setup_py PASSED [ 74%]
elipy2/tests/test_retry.py::test_retry_with_exception PASSED             [ 74%]
elipy2/tests/test_retry.py::test_retry_with_no_exception PASSED          [ 74%]
elipy2/tests/test_retry.py::test_retry_with_settings_disabled PASSED     [ 74%]
elipy2/tests/test_retry.py::test_retry_with_return_value PASSED          [ 74%]
elipy2/tests/test_retry.py::TestMetadata::test_retry_metadata PASSED     [ 74%]
elipy2/tests/test_retry.py::TestMetadata::test_retry_mocking PASSED      [ 74%]
elipy2/tests/test_running_processes.py::TestCoreModule::test_kill PASSED [ 74%]
elipy2/tests/test_running_processes.py::TestCoreModule::test_kill_list PASSED [ 74%]
elipy2/tests/test_running_processes.py::TestCoreModule::test_kill_nothing_not_a_process PASSED [ 74%]
elipy2/tests/test_running_processes.py::TestCoreModule::test_kill_nothing PASSED [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks PASSED   [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_ps5_tools PASSED [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_ps5_tools_include_wfmfd PASSED [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_ps5_tools_skip_version_checks PASSED [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_one_platform PASSED [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_shift_build PASSED [ 74%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_xb1_old PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_xb1_new PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_not_console PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_credstore_called PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_credstore_not_called PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_fbenv_with_platform PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_fbenv_without_platform PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_non_ps5_pre2022 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_non_ps5_2022 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_ps5_2022 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_ps5_pre2022 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_ps5_pre2022_failure PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_ps5_2024 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_ps5_2023 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_update_shell PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_check_installed PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_2021 PASSED [ 75%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_pre2021 PASSED [ 76%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_exception PASSED [ 76%]
elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_p4 PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_real_secret_output_path PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_real_secret_output_path_tnt PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_credential_file_store PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_credential_file_store_invalid_key PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_connect_approle PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_connect_token PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_get_value PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_get_value_secret_key PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secret PASSED        [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_already_downloaded PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_requires_downloaded PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_retry[UnexpectedError] PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_retry[ConnectionError] PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_to_file PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_secret_matches_context PASSED [ 76%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secrets PASSED       [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secrets_none PASSED  [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_get_secrets_from_ess PASSED [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_user_password PASSED [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_credentialfile PASSED [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_fails PASSED [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_runfails PASSED [ 77%]
elipy2/tests/test_secrets.py::TestSecrets::test_remove_old_frosty_cert PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerDefaultLocation::test_default PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestGetProviderSettings::test_get_provider_settings PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestGetProviderSettings::test_get_provider_settings_no_settings PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_class_overrides PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_url_overrides_primary PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_index_overrides_primary PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_url_overrides_secondary PASSED [ 77%]
elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_index_overrides_secondary PASSED [ 77%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload PASSED      [ 77%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_raises_elipyexception_on_non_zero_shift_executable_exit_code PASSED [ 77%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_logs_error_on_on_failure PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_find_shift_builds PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_find_shift_builds_no_files PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_find_shift_builds_no_path PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_shift_copy PASSED      [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_shift_copy_no_files PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_shift_register_shift_in_bilbo PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_sup_file PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_sup_file_retail PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_fail PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_fail_sku PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_fail2 PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_name_too_long PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_remastered PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_reshift_check PASSED   [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_reshift_check_false PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_reshift_check_no_bilbo PASSED [ 78%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload PASSED          [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_patch_remaster PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_reshift PASSED  [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_no_bilbo PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_fake_sku PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_with_exception PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_with_elipyexception PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_create_template_file PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_create_template_file_file_exist PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_create_template_file_without_supplemental PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_read_shift_file PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_read_shift_file2 PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data_empty PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data_version_and_upload_loop PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data_content_layer PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_config_path PASSED [ 79%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_config_data PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_without_build_and_sku_name_different_code_data_changelists PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_without_build_and_sku_name_same_code_data_changelists PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_with_build_and_sku_name_different_code_data_changelists PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_with_build_and_sku_name_same_code_data_changelists PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_parse_branch_data PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_parse_branch_code PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_changelist_label_both PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_changelist_label_code_only PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_find_builds_bilbo PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_set_retention_policy PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_download_files SKIPPED (Not running on windows) [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_submission_tool PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_call_process_shift_upload_directly PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_call_determine_build_location_directly PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_extract_build_id PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_extract_build_id_not_found PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_build_set_retention_policy PASSED [ 80%]
elipy2/tests/test_shift_utils.py::TestShift::test_wait_until_build_available PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_wait_until_build_available_no_build_id PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_build_status_api_call PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_build_status_return PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_get_latest_build_id_by_sku PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_single_build_with_incremental_delivery_and_build_id_set PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_single_build_with_incremental_delivery_and_no_build_id PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_upload_single_build_without_incremental_delivery PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_with_base_build_id PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_without_base_build_id PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_files_present PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_upload_loop_files_present PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_validate_supplemental_files_present PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path_with_content_layer PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path_with_content_layer_lowercase PASSED [ 81%]
elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path_with_combine PASSED [ 81%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_determine_build_location_returns_a_str_path[branch1-changelist1-//filer.test/builds/DICE/userbuild/test_user/offsite_basic_drone/branch1/changelist1] PASSED [ 81%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_determine_build_location_returns_a_str_path[branch2-changelist2-//filer.test/builds/DICE/userbuild/test_user/offsite_basic_drone/branch2/changelist2] PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_determine_build_location_returns_a_str_path[branch3-changelist3-//filer.test/builds/DICE/userbuild/test_user/offsite_basic_drone/branch3/changelist3] PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_generate_shift_template_act_according_to_skuid[filer\\path\\final-shift_data0-1] PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_generate_shift_template_act_according_to_skuid[filer\\path\\final-shift_data1-0] PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_generate_shift_template_act_according_to_skuid[filer\\path\\final-shift_data2-0] PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_child_class_init_uses_super_defaults PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_child_class_init_overwrites_super_kwargs PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_platform_instance_variable_set_to_tools PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_initialization_uses_super_defaults_when_passed_param_is_set PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_initialization_exception_raised_when_passed_param_is_none PASSED [ 82%]
elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_child_class_kwarg_is_passed_to_super_init PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_child_class_init_uses_super_defaults PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_child_class_init_overwrites_super_kwargs PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_platform_instance_variable_set_to_tools PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_initialization_uses_super_defaults_when_passed_param_is_set PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_initialization_exception_raised_when_required_param_is_none PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_child_class_kwarg_is_passed_to_super_init PASSED [ 82%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_determine_build_location_offsite_drone_non_zipped PASSED [ 83%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_determine_build_location_offsite_drone_zipped PASSED [ 83%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_process_shift_upload PASSED [ 83%]
elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_process_shift_upload_failure PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_child_class_init_uses_super_defaults PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_child_class_init_overwrites_super_kwargs PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_initialization_uses_super_defaults_when_passed_param_is_set PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_initialization_exception_raised_when_required_param_is_none PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_child_class_kwarg_is_passed_to_super_init PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_process_shift_upload PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_process_shift_upload_failure PASSED [ 83%]
elipy2/tests/test_shifters.py::TestFrostyShifter::test_determine_build_location PASSED [ 83%]
elipy2/tests/test_shifters.py::TestShiftParameterSetting::test_child_class_kwarg_is_passed_to_super_init PASSED [ 83%]
elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_frosty PASSED [ 83%]
elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_offiste_drone PASSED [ 83%]
elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_offsite_basic_drone PASSED [ 83%]
elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_failure PASSED [ 83%]
elipy2/tests/test_steam_utils.py::TestSteamUtils::test_download_steam_sdk PASSED [ 83%]
elipy2/tests/test_steam_utils.py::TestSteamUtils::test_retrieve_steam_credentials PASSED [ 84%]
elipy2/tests/test_steam_utils.py::TestSteamUtils::test_log_in_to_steam_and_run PASSED [ 84%]
elipy2/tests/test_steam_utils.py::TestSteamUtils::test_log_in_to_steam_and_run_no_quit PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test_init PASSED              [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__verify_playstation_symbol_store_upload PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__verify_playstation_symbol_store_upload_file_missing PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__win64_symbol_pattern PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_file PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation__run_playstation_symupload_core_exception PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_missing_source_indexing PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_path PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_no_compress PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_with_log PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_with_force PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_with_source_indexing PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__run_playstation_symupload PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test__run_playstation_symupload_not_in_path PASSED [ 84%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols PASSED    [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_with_custom_includes PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_single_file PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_tools PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index PASSED      [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_path PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_fake PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4 PASSED  [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps5 PASSED  [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_win64_binaries PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_linux PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_tool PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4_binaries PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps5_binaries PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4_binaries_no_source_index_file PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4_binaries_no_file PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_file_without_source PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_file_with_makedirs PASSED [ 85%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_args PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_branch PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_changelist PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_changelist_with_platform_release PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_platform PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_config PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_invalid_platform PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity_command_failed PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity_not_enough_arguments PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity_found_large_pdb PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_nocompress PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_no_store PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_default_value PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_retry PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_ps4 PASSED [ 86%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_ps4_nocompress PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_symfiles PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_find_symstore_exe_file PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_linux PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_game_binary PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_upload_game_binary_retail PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin PASSED  [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_with_path PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_trial PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_no_dir PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_no_file PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_find_symbol_files PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_find_symbol_files_no_dir PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_find_tools_location PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_find_tools_location_not_found PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols_add_files PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_strip_no_linux64_file PASSED [ 87%]
elipy2/tests/test_symbols.py::TestSymbols::test_strip_no_buildinfo_file PASSED [ 88%]
elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols_non_cygwin PASSED [ 88%]
elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols_non_existing_build_path PASSED [ 88%]
elipy2/tests/test_symbols.py::TestSymbols::test_check_symbol_size_big PASSED [ 88%]
elipy2/tests/test_symbols.py::TestSymbols::test_copy_symbols_to_kobold_inbox PASSED [ 88%]
elipy2/tests/test_symbols.py::TestSymbols::test_copy_symbols_to_kobold_inbox_no_inbox_for_branch PASSED [ 88%]
elipy2/tests/test_symbols.py::TestSymbols::test_copy_symbols_to_kobold_inbox_no_settings PASSED [ 88%]
elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_run_upload_symbols_to_sym_store_with_pdb_error_ignored PASSED [ 88%]
elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_run_upload_symbols_to_sym_store_with_pdb_error_not_ignored PASSED [ 88%]
elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_playstation_symbol_store_upload_with_pdb_error_ignored PASSED [ 88%]
elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_playstation_symbol_store_upload_with_pdb_error_not_ignored PASSED [ 88%]
elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_verify_playstation_symbol_store_upload_with_pdb_error_ignored PASSED [ 88%]
elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_verify_playstation_symbol_store_upload_with_pdb_error_not_ignored PASSED [ 88%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_get_data PASSED     [ 88%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_get_elasticsearch_index PASSED [ 88%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_get_function_data PASSED [ 88%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_log_error_to_es PASSED [ 88%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_modify_args PASSED  [ 88%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_modify_dict PASSED  [ 89%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_upload_metrics PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestCoreModule::test_upload_metrics_fail PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestMetadata::test_correct_metadata PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestMetadata::test_retry_mocking_attributes PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestAddBreadcrumbs::test_add_breadcrumb PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestDropTelemetryEvents::test PASSED     [ 89%]
elipy2/tests/test_telemetry.py::TestGetMachineData::test_windows PASSED  [ 89%]
elipy2/tests/test_telemetry.py::TestGetFrostbiteData::test_not_fbenv_environment PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestGetFrostbiteData::test_valid_environment_vars PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestGetJenkinsData::test_not_jenkins_environment PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestGetJenkinsData::test_is_jenkins_environment PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestGetAzurePipelineData::test_not_azure_environment PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestGetAzurePipelineData::test_is_azure_environment_linux PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestGetAzurePipelineData::test_is_azure_environment_windows PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs_casing PASSED [ 89%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs_none PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs_custom_object PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_in_value PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_in_list_value PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_in_dict_value PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestCreateDictFromArgs::test_with_args PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestCreateDictFromArgs::test_with_kwargs PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[a_user] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[some_s3cr3t_pa55w0rd] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[https://some_site.ea.com] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[D:\\some_path] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[/MT:12] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[start:end] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[_password_] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[_token_] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[_apikey_] PASSED [ 90%]
elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[*******************************.ea.com] PASSED [ 90%]
elipy2/tests/test_testutils.py::TestGotButExpected::test_got_but_expected PASSED [ 90%]
elipy2/tests/test_usage.py::TestSleepUsage::test_never_sleep_more_than_30_seconds PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_save_symbols_locally_clean PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_anything_with_checksum PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_anything_with_checksum_no_dest PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_save_vaulting_logs PASSED    [ 91%]
elipy2/tests/test_vault.py::TestVault::test_save_symbols_locally PASSED  [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_symbols_nolog PASSED   [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_build PASSED           [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_build_no_log PASSED    [ 91%]
elipy2/tests/test_vault.py::TestVault::test_validate_build_files PASSED  [ 91%]
elipy2/tests/test_vault.py::TestVault::test_validate_build_files_notset PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_validate_build_files_no_files PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_symbols_with_symstore_error_ignored PASSED [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault PASSED                 [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_build_dry_run PASSED   [ 91%]
elipy2/tests/test_vault.py::TestVault::test_vault_symbols_dry_run PASSED [ 91%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_buildmachine_check PASSED [ 91%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_buildmachine_check_false PASSED [ 91%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_carbonblack PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_convert_bytes_to_human_readable PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_create_reg PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_delete_service_pass PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_get_computer_name PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_get_free_disk_space SKIPPED (Not running on windows) [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_get_reg PASSED  [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_not_installed PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_running PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_stopped PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_unknown PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_service_installed_false PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_service_installed_true PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_set_reg PASSED  [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_shutdown_service PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_shutdown_service_error PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_shutdown_service_running PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_start_service PASSED [ 92%]
elipy2/tests/test_windows_tools.py::TestCoreModule::test_time PASSED     [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestAvalanche::test_init PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestTriggerGc::test_get_cache_summary PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestTriggerGc::test_get_cache_summary_return_type PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestDropCacheBucket::test_drop_cache_bucket PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestDropCacheBucket::test_drop_cache_bucket_return_type PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestPutCacheBucket::test_put_cache_bucket PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestPutCacheBucket::test_put_cache_bucket_return_type PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestGetCacheValue::test_put_cache_bucket PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestGetCacheValue::test_put_cache_bucket_return_type PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCore::test_init PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCore::test_connect PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCore::test_disconnect PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[/some-http://test-av-server.test.dre.dice.se/some] PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[/some/nested-http://test-av-server.test.dre.dice.se/some/nested] PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[/some/nested/url-http://test-av-server.test.dre.dice.se/some/nested/url] PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[some-http://test-av-server.test.dre.dice.se/some] PASSED [ 93%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url_return_type PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_return_type PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_bad_url PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_connection_timeout PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_connection_error PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put_return_type PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put_bad_error_code_lower_bound PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put_bad_error_code_upper_bound PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post_return_type PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post_bad_error_code_lower_bound PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post_bad_error_code_upper_bound PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete_return_type PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete_bad_error_code_lower_bound PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete_bad_error_code_upper_bound PASSED [ 94%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestStorage::test_init PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDb::test_get_db PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDb::test_get_db_return_type PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDbAll::test_get_db PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDbAll::test_get_db_return_type PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExists::test_db_exists_return_type PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExists::test_db_exists_true PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExists::test_db_exists_false PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_true[BattlefieldGameData] PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_true[Data.kin] PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_true[ev.Win32.Debug] PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_return_type PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_false PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_true[BattlefieldGameData] PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_true[Data.kin] PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_true[ev.Win32.Debug] PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_false PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetBuiltLevels::test_get_built_levels PASSED [ 95%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetBuiltLevels::test_get_built_levels_return_type PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestPutBuiltLevels::test_put_built_levels PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestPutBuiltLevels::test_put_built_levels_return_type PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDeleteDatabase::test_delete_database PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDeleteDatabase::test_delete_database_return_type PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDeleteDatabase::test_delete_empty_database PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestStorage::test_init PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestStorage::test_components PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestGetStatus::test_get_status PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestGetStatus::test_get_status_return_type PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestAvalanche::test_init PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc_return_type PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc_force PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc_async PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestForceGc::test_force_gc PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestForceGc::test_force_gc_return_type PASSED [ 96%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestForceGc::test_force_gc_async PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestPoolPrimaryInfo::test_get_pool_primary_info PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestPoolPrimaryInfo::test_get_pool_primary_info_return_type PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestGetPoolInfo::test_get_pool_info PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestGetPoolInfo::test_get_pool_info_return_type PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestSizeOfFmt::test_sizeof_fmt PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTrimExtents::test_trim_extents PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestRebalanace::test_rebalance PASSED [ 97%]
elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestEvacuateOverflow::test_evacuate_overflow PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid_02 PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid_upper_case PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid_upper_and_lower_match PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetTestDataUuid::test_get_autotest_test_uuid PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetTestDataUuid::test_get_autotest_test_uuid_upper_case PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetTestDataUuid::test_get_autotest_test_uuid_upper_and_lower_match PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetByUuid::test_get_by_uuid PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetFlattenedAutotestData::test_get_test_data_from_build_json PASSED [ 97%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetFlattenedAutotestData::test_get_testcategory_data_from_build_json PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetVerifiedDataFromTestData::test_get_verified_data_from_test_data_mismatch PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetVerifiedDataFromTestData::test_get_verified_data_from_test_data_should_not_raise PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_categories_data_match PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_openbetatests_data_match PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_test_platform_data_match PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_individualtest_data_match PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_test_data_match PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_test_category_data_match PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build_dump_attributes[True-1] PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build_dump_attributes[False-0] PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::TestRegisterAutotestCategoryData::test_register_autotest_category_data PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::TestRegisterAutotestCategoryData::test_register_autotest_category_data_dump_attributes[True-1] PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::TestRegisterAutotestCategoryData::test_register_autotest_category_data_dump_attributes[False-0] PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build_body PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_test_data PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBuildDataFromBuildJson::test_get_build_data_from_build_json PASSED [ 98%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBasicDataFromBuildJson::test_get_basic_data_from_build_json_original_unmodified PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBasicDataFromBuildJson::test_get_basic_data_from_build_json_matching_attributes PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBasicDataFromBuildJson::test_get_basic_data_from_build_json_matching_verified_len PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBySource::test_get_by_source PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBySource::test_get_by_source_default PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBySource::test_get_by_source_include_deleted PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_get_attributes PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_no_verified_data PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_filtered_data[drone] PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_filtered_data[code] PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestDumpAttributes::test_dump_attributes PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestDumpAttributes::test_dump_attributes_retry[IndexError] PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestDumpAttributes::test_dump_attributes_retry[PermissionError] PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_build_data PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_build_data_passed_data PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_build_data_no_key_verified_data PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_code_build_data PASSED [ 99%]
elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetCategoryDataBySourceUuid::test_get_category_data_by_source_uuid PASSED [100%]

=================================== FAILURES ===================================
_ TestExpire.test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception _

self = <elipy2.tests.test_expire.TestExpire object at 0x7f39bcd016d0>
mock_get_builds_to_expire = <MagicMock name='get_builds_to_expire' id='139885869326736'>
mock_logger = <MagicMock name='LOGGER' id='139885869866000'>
mock_delete_with_onefs_api = <MagicMock name='delete_with_onefs_api' id='139885869606736'>
fixture_builds = [{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\buil...t\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...]

    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.expire.LOGGER")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception(
        self,
        mock_get_builds_to_expire,
        mock_logger,
        mock_delete_with_onefs_api,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_with_onefs_api.side_effect = ELIPYException("Test exception")
        ExpireUtils().expire("//not/a/build", 0, dry_run=False, use_onefs_api=True)
>       assert mock_logger.error.call_count == len(fixture_builds)
E       assert 9 == 8
E        +  where 9 = <MagicMock name='LOGGER.error' id='139885870060752'>.call_count
E        +    where <MagicMock name='LOGGER.error' id='139885870060752'> = <MagicMock name='LOGGER' id='139885869866000'>.error
E        +  and   8 = len([{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2", "type": "drone"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2018-10-28", "deleted": "5", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {}}, {"_id": "\\\\filer.test\\builds\\dice\\"}, {"_id": "\\\\filer.test\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...])

elipy2/tests/test_expire.py:214: AssertionError
_ TestExpire.test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception _

self = <elipy2.tests.test_expire.TestExpire object at 0x7f39bcd01d90>
mock_get_builds_to_expire = <MagicMock name='get_builds_to_expire' id='139885869097104'>
mock_logger = <MagicMock name='LOGGER' id='139885869865680'>
mock_delete_folder_with_robocopy = <MagicMock name='delete_folder_with_robocopy' id='139885870134032'>
fixture_builds = [{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\buil...t\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...]

    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.LOGGER")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception(
        self,
        mock_get_builds_to_expire,
        mock_logger,
        mock_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_folder_with_robocopy.side_effect = ELIPYException("Test exception")
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)
>       assert mock_logger.error.call_count == len(fixture_builds)
E       assert 9 == 8
E        +  where 9 = <MagicMock name='LOGGER.error' id='139885869066256'>.call_count
E        +    where <MagicMock name='LOGGER.error' id='139885869066256'> = <MagicMock name='LOGGER' id='139885869865680'>.error
E        +  and   8 = len([{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2", "type": "drone"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2018-10-28", "deleted": "5", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {}}, {"_id": "\\\\filer.test\\builds\\dice\\"}, {"_id": "\\\\filer.test\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...])

elipy2/tests/test_expire.py:229: AssertionError
_____________________ TestExpire.test_get_builds_to_expire _____________________

self = <elipy2.tests.test_expire.TestExpire object at 0x7f39bcd15dd0>
fixture_metadata_manager = <MagicMock spec='BuildMetadataManager' id='139885868920592'>
fixture_builds = [{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\buil...t\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...]

    def test_get_builds_to_expire(
        self,
        fixture_metadata_manager,
        fixture_builds,
    ):
        # Test that builds get properly processed through the new integrated logic
        fixture_metadata_manager.get_build_ids.return_value = ["one", "two", "three"]
        fixture_metadata_manager.get_builds_matching.return_value = fixture_builds[0:1]
    
        # Mock scanning and querying
        with patch("elipy2.expire.ExpireUtils._scan_disk_builds") as mock_scan, patch(
            "elipy2.expire.ExpireUtils._query_bilbo_for_build"
        ) as mock_query:
            mock_scan.return_value = ["one", "two", "three"]
            mock_query.return_value = fixture_builds[0:1]
    
            result = ExpireUtils().get_builds_to_expire("\\\\not\\a\\real\\bilbo\\address", 5, 1)
>           assert result == ([], [])
E           AssertionError: assert ([], ['one', 'two', 'three']) == ([], [])
E             At index 1 diff: ['one', 'two', 'three'] != []
E             Full diff:
E             - ([], [])
E             + ([], ['one', 'two', 'three'])

elipy2/tests/test_expire.py:341: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo initalized with endpoint http://bilbo-es-test.test.se and index bilbo_v2
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo version 2
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b780b090>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b780b090>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b780b090>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b780b090>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b06d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b06d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b06d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b06d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0350>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0350>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0350>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0350>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0390>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0390>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0390>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0390>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0f10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Failed to update Bilbo entry: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0250>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0250>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0250>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0250>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error in _query_all_bilbo_indices_for_build: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0250>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error checking age for build one: [Errno 2] No such file or directory: 'one'. Treating as orphan for deletion.
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo initalized with endpoint http://bilbo-es-test.test.se and index bilbo_v2
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo version 2
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0f90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0610>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b02d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b02d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b02d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b02d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b05d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0d90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b0410>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Failed to update Bilbo entry: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error in _query_all_bilbo_indices_for_build: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77b01d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error checking age for build two: [Errno 2] No such file or directory: 'two'. Treating as orphan for deletion.
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo initalized with endpoint http://bilbo-es-test.test.se and index bilbo_v2
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo version 2
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b7716ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716ed0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77168d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77168d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77168d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77168d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716f10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b7716f10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716f10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716f10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716950>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b7716950>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716950>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716950>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b7716610>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77165d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77165d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77165d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77165d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77162d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77162d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77162d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77162d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716e10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b7716e10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716e10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716e10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77169d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77169d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77169d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77169d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Failed to update Bilbo entry: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716450>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b7716450>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716450>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716450>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error in _query_all_bilbo_indices_for_build: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b7716450>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error checking age for build three: [Errno 2] No such file or directory: 'three'. Treating as orphan for deletion.
2025-07-07 11:13:33 elipy2 [INFO]: Orphaned builds processing complete: 3 orphaned (deleted or not found in any Bilbo index), 0 kept for retention processing
2025-07-07 11:13:33 elipy2 [INFO]: Orphan builds to be deleted:
2025-07-07 11:13:33 elipy2 [INFO]:   one (age: unknown)
2025-07-07 11:13:33 elipy2 [INFO]:   two (age: unknown)
2025-07-07 11:13:33 elipy2 [INFO]:   three (age: unknown)
2025-07-07 11:13:33 elipy2 [INFO]: After orphan deletion, builds available 0 <= 5 maximum builds
__________________ TestExpire.test_get_builds_to_expire_multi __________________

self = <elipy2.tests.test_expire.TestExpire object at 0x7f39bccfd690>
mock_scan_disk_builds = <MagicMock name='_scan_disk_builds' id='139885867629968'>
mock_query_bilbo_for_build = <MagicMock name='_query_bilbo_for_build' id='139885867629136'>

    @patch("elipy2.expire.ExpireUtils._query_bilbo_for_build")
    @patch("elipy2.expire.ExpireUtils._scan_disk_builds")
    def test_get_builds_to_expire_multi(
        self,
        mock_scan_disk_builds,
        mock_query_bilbo_for_build,
    ):
        # Mock disk scanning to return three build paths
        mock_scan_disk_builds.return_value = [
            r"\\filer.test\builds\DICE\one",
            r"\\filer.test\builds\DICE\two",
            r"\\filer.test\builds\DICE\three",
        ]
    
        def mock_bilbo_query(build_path):
            if "one" in build_path:
                return [
                    elipy2.bilbo.Build().from_dict(
                        {"_id": build_path, "_source": {"created": "1", "type": "code"}}
                    )
                ]
            elif "two" in build_path:
                return [
                    elipy2.bilbo.Build().from_dict(
                        {"_id": build_path, "_source": {"created": "2", "type": "code"}}
                    )
                ]
            elif "three" in build_path:
                return [
                    elipy2.bilbo.Build().from_dict(
                        {"_id": build_path, "_source": {"created": "3", "type": "code"}}
                    )
                ]
            return []
    
        mock_query_bilbo_for_build.side_effect = (
            mock_bilbo_query  # Test with maxamount=1, should keep 1 build and delete 2
        )
        result = ExpireUtils().get_builds_to_expire("\\\\not\\a\\real\\bilbo\\address", 1, 1)
        builds_to_delete, orphaned_builds = result
>       assert builds_to_delete == [
            r"\\filer.test\builds\DICE\one",
            r"\\filer.test\builds\DICE\two",
        ]
E       AssertionError: assert [] == ['\\\\filer.test\\builds\\DICE\\one', '\\\\filer.test\\builds\\DICE\\two']
E         Right contains 2 more items, first extra item: '\\\\filer.test\\builds\\DICE\\one'
E         Full diff:
E         - ['\\\\filer.test\\builds\\DICE\\one', '\\\\filer.test\\builds\\DICE\\two']
E         + []

elipy2/tests/test_expire.py:383: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo initalized with endpoint http://bilbo-es-test.test.se and index bilbo_v2
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo version 2
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3190>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3190>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3190>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3190>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f59d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5d10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Failed to update Bilbo entry: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error in _query_all_bilbo_indices_for_build: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f5dd0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error checking age for build \\filer.test\builds\DICE\one: [Errno 2] No such file or directory: '\\\\filer.test\\builds\\DICE\\one'. Treating as orphan for deletion.
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo initalized with endpoint http://bilbo-es-test.test.se and index bilbo_v2
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo version 2
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fae90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fae90>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fae90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fae90>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3a10>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Failed to update Bilbo entry: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error in _query_all_bilbo_indices_for_build: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f3550>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error checking age for build \\filer.test\builds\DICE\two: [Errno 2] No such file or directory: '\\\\filer.test\\builds\\DICE\\two'. Treating as orphan for deletion.
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo initalized with endpoint http://bilbo-es-test.test.se and index bilbo_v2
2025-07-07 11:13:33 elipy2 [INFO]: Bilbo version 2
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f83d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77f83d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f83d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77f83d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa1d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa1d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa1d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa1d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa990>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa990>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa990>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa990>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa690>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa690>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa690>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa690>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa210>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa210>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa210>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa210>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa050>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa050>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa050>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa050>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa610>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa610>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [INFO]: Failed to update bilbo entry, retrying: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa6d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Failed to update Bilbo entry: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa3d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/usr/local/lib/python3.7/socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 416, in _make_request
    conn.request(method, url, **httplib_request_kw)
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 244, in request
    super(HTTPConnection, self).request(method, url, body=body, headers=headers)
  File "/usr/local/lib/python3.7/http/client.py", line 1252, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1298, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1247, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/usr/local/lib/python3.7/http/client.py", line 1026, in _send_output
    self.send(msg)
  File "/usr/local/lib/python3.7/http/client.py", line 966, in send
    self.connect()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 205, in connect
    conn = self._new_conn()
  File "/usr/local/lib/python3.7/site-packages/urllib3/connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f39b77fa3d0>: Failed to establish a new connection: [Errno -2] Name does not resolve

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/usr/local/lib/python3.7/site-packages/urllib3/connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/usr/local/lib/python3.7/site-packages/urllib3/util/retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa3d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/builds/dre-cobra/elipy/elipy2/elipy2/bilbo.py", line 183, in post_call
    response = self.session.post(url, data=serialized_data, headers=header)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.7/site-packages/requests/adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa3d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error in _query_all_bilbo_indices_for_build: HTTPConnectionPool(host='bilbo-es-test.test.se', port=80): Max retries exceeded with url: /bilbo_v2/build/_search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f39b77fa3d0>: Failed to establish a new connection: [Errno -2] Name does not resolve'))
2025-07-07 11:13:33 elipy2 [ERROR]: Error checking age for build \\filer.test\builds\DICE\three: [Errno 2] No such file or directory: '\\\\filer.test\\builds\\DICE\\three'. Treating as orphan for deletion.
2025-07-07 11:13:33 elipy2 [INFO]: Orphaned builds processing complete: 3 orphaned (deleted or not found in any Bilbo index), 0 kept for retention processing
2025-07-07 11:13:33 elipy2 [INFO]: Orphan builds to be deleted:
2025-07-07 11:13:33 elipy2 [INFO]:   \\filer.test\builds\DICE\one (age: unknown)
2025-07-07 11:13:33 elipy2 [INFO]:   \\filer.test\builds\DICE\two (age: unknown)
2025-07-07 11:13:33 elipy2 [INFO]:   \\filer.test\builds\DICE\three (age: unknown)
2025-07-07 11:13:33 elipy2 [INFO]: After orphan deletion, builds available 0 <= 1 maximum builds
=============================== warnings summary ===============================
elipy2/tests/test_usage.py:9
  /builds/dre-cobra/elipy/elipy2/elipy2/tests/test_usage.py:9: PytestUnknownMarkWarning: Unknown pytest.mark.last - is this a typo?  You can register custom marks to avoid this warning - for details, see https://docs.pytest.org/en/stable/how-to/mark.html
    @pytest.mark.last

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

----------- coverage: platform linux, python 3.7.7-final-0 -----------
Name                                    Stmts   Miss Branch BrPart  Cover   Missing
-----------------------------------------------------------------------------------
elipy2/__init__.py                         96      4     24      2  95.0%   20-22, 157, 184->188
elipy2/artifactory_client.py               32      0      8      0 100.0%
elipy2/avalanche.py                       615      1    156      1  99.7%   28
elipy2/avalanche_web_api/cache.py          29      1      4      1  93.9%   46
elipy2/avalanche_web_api/core.py           73     10     12      2  83.5%   34-44, 70->72, 88->90
elipy2/avalanche_web_api/database.py       75     13     22      0  84.5%   103-114, 133-137
elipy2/avalanche_web_api/server.py         22      0      0      0 100.0%
elipy2/avalanche_web_api/storage.py        52      2      4      2  92.9%   29-30
elipy2/aws.py                              48     35     10      0  22.4%   27-41, 47, 53-64, 74-104, 111-131
elipy2/az_utils.py                         33      4      2      1  85.7%   25-31, 102
elipy2/azcopy_client.py                    60     11      6      0  83.3%   204-211, 221-223
elipy2/bilbo.py                           708    133    294     32  78.9%   76, 85-86, 95-96, 106, 109, 116, 159, 169, 188, 190-191, 206, 212-240, 254-267, 301-305, 314, 331, 337-339, 347, 354, 363-365, 414, 440, 443, 461-472, 478-482, 524-525, 566, 647, 651, 655, 673, 677, 681, 699, 701, 708-712, 825-830, 841, 852, 883-884, 1062-1063, 1069-1075, 1145-1146, 1324->1330, 1337-1341, 1369-1371, 1400-1402, 1432, 1514->1513, 1523-1526, 1548, 1551, 1582-1584, 1589->1594, 1640
elipy2/bilbo_v2.py                        272     23     78      8  89.4%   103->102, 270, 406, 423-424, 460, 540-545, 547->550, 587, 641-644, 650-655, 670-673, 709->699
elipy2/build_metadata.py                  252     29     16      1  84.3%   466, 473, 480, 537-564, 830-831, 837-838, 844-845, 880-881, 980-981
elipy2/build_metadata_utils.py             27      1     10      2  91.9%   19, 37->40
elipy2/cli.py                              74     28     28      4  56.9%   21->41, 22-35, 47-48, 87, 90-95, 120-126
elipy2/code.py                            248      0     84      0 100.0%
elipy2/command_logger.py                   87      4     22      4  92.7%   62, 75->exit, 86, 100, 118
elipy2/config.py                           69      7     34      3  88.3%   44-49, 67, 98-101
elipy2/core.py                            537     53    181     25  88.0%   60-66, 129, 321, 322->324, 342, 343->336, 389, 415, 456, 471, 490, 538-539, 543->546, 665->664, 719-721, 733-735, 741, 772-774, 785-786, 853-863, 905, 908-937, 953, 963-965, 1036->1039, 1122->1124, 1124->1126, 1126->1128, 1128->1131, 1138, 1142
elipy2/custom_logging.py                   53      0      6      0 100.0%
elipy2/data.py                            196      0     64      0 100.0%
elipy2/denuvo.py                          177      1     66      4  97.9%   41->40, 68->67, 139->142, 364, 428->431
elipy2/enums_utils.py                      13      0      0      0 100.0%
elipy2/exceptions.py                       25      1      2      1  92.6%   113
elipy2/expire.py                          487    304    228     20  32.6%   32->34, 68-70, 83-84, 89->97, 123-137, 148, 152-159, 188->exit, 198-199, 205-214, 218-232, 236-261, 272-313, 323-358, 368-395, 404-420, 425-455, 462-473, 479-481, 488-489, 516-519, 526-544, 550, 565-582, 593-605, 614->624, 619-620, 637->639, 644->631, 668, 675, 683-687, 690->exit, 693-703, 710-724, 728-731, 756-760, 765-766, 777-797, 801-809, 813-834, 840-847, 853-860, 864-879, 885-893, 899-902, 908-911
elipy2/filer.py                           473      1    184      2  99.5%   182, 1353->exit
elipy2/filer_paths.py                     101      0     26      0 100.0%
elipy2/frostbite/__init__.py                4      2      2      1  50.0%   8-10
elipy2/frostbite/build_agent_utils.py      31      2     18      2  91.8%   33->35, 51-52
elipy2/frostbite/fbcli.py                 142      3     46      2  96.3%   182-184, 208->211
elipy2/frostbite/fbenv_layer.py           227      0     80      0 100.0%
elipy2/frostbite/icepick.py               212     28     64      7  85.1%   72->exit, 81-82, 128-129, 138-139, 153->159, 158, 189, 264-273, 383-384, 395-396, 440-447, 456, 459, 497
elipy2/frostbite/licensee_utils.py         13      0      0      0 100.0%
elipy2/frostbite/package_utils.py          17      1      2      0  94.7%   106
elipy2/frostbite/sdk_utils.py              74      0     30      1  99.0%   106->140
elipy2/frostbite_core.py                  111     11     56      4  88.6%   151-155, 160, 169-174, 219, 223
elipy2/local_paths.py                     126      0     44      0 100.0%
elipy2/multiprocessing.py                  10      0      0      0 100.0%
elipy2/oreans.py                           73     14     24     11  74.2%   33->36, 55-57, 65, 89-90, 95-96, 132, 141, 144->147, 148-149, 165-166, 168->171, 173->exit
elipy2/p4.py                              686     12    393      4  98.0%   62->64, 64->66, 137-152, 208, 530
elipy2/package.py                         114     11     42      9  85.9%   66, 133, 167-170, 173, 178, 196-197, 251, 287->294, 295
elipy2/reqs_utils.py                       64      8     26      5  85.6%   43, 47, 132-133, 139-140, 143-144
elipy2/retry_utils.py                      21      2      4      2  84.0%   28, 47
elipy2/run.py                              45     12     14      1  67.8%   68-69, 73-85
elipy2/running_processes.py                23      4      8      1  83.9%   22-23, 25->exit, 43-44
elipy2/secrets.py                         117      8     41      3  93.0%   153->159, 195-208, 218, 238, 299-303
elipy2/shift_utils.py                     516     94    278     46  79.6%   180-181, 214, 220, 259->258, 265->264, 275->274, 304, 306, 308, 310, 312, 314, 316, 318, 322, 324, 326, 328, 330, 332, 334, 336, 338, 340, 345, 364-365, 393, 418-420, 428->431, 459-462, 521, 599, 605, 607-608, 612, 640-642, 687, 695->700, 702-707, 773->772, 778-779, 794, 803, 832, 857-860, 870-881, 898-909, 923-924, 929, 964-982, 990-1003, 1010-1014, 1068
elipy2/shifters.py                         64      0     14      0 100.0%
elipy2/steam_utils.py                      44      2      2      0  95.7%   103-104
elipy2/symbols.py                         477     46    220     22  88.2%   46->exit, 138, 189, 227-229, 233->181, 415, 426, 472, 521->513, 544->556, 551->544, 557-558, 697->700, 700->639, 720-738, 749, 826-828, 830-834, 848, 861->866, 902->919, 910-911, 940-941, 1005-1007, 1027-1036
elipy2/telemetry.py                       210     10     62      5  93.8%   32-33, 65, 169->168, 184-186, 301, 387-390, 405->407, 424->426
elipy2/vault.py                            78      3     30      4  93.5%   119->167, 129, 165, 181
elipy2/windows_tools.py                   123     16     22      5  85.5%   18-19, 36->43, 132-135, 145-146, 158-159, 208, 209->212, 229-235
-----------------------------------------------------------------------------------
TOTAL                                    8556    955   3093    250  86.9%

Required test coverage of 86.0% reached. Total coverage: 86.89%
============================= slowest 10 durations =============================
1.01s call     elipy2/tests/test_core.py::TestCoreModule::test_run_async_stop_all_normal
0.30s setup    elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_xbsx
0.24s setup    elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state
0.20s setup    elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_variants
0.18s setup    elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbenv_exception
0.17s setup    elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_normalize_platform
0.12s call     elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire
0.12s call     elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_platform
0.12s call     elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire_multi
0.11s call     elipy2/tests/test_core.py::TestCoreModule::test_run_async_join
=========================== short test summary info ============================
FAILED elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception - assert 9 == 8
 +  where 9 = <MagicMock name='LOGGER.error' id='139885870060752'>.call_count
 +    where <MagicMock name='LOGGER.error' id='139885870060752'> = <MagicMock name='LOGGER' id='139885869866000'>.error
 +  and   8 = len([{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2", "type": "drone"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2018-10-28", "deleted": "5", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {}}, {"_id": "\\\\filer.test\\builds\\dice\\"}, {"_id": "\\\\filer.test\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...])
FAILED elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception - assert 9 == 8
 +  where 9 = <MagicMock name='LOGGER.error' id='139885869066256'>.call_count
 +    where <MagicMock name='LOGGER.error' id='139885869066256'> = <MagicMock name='LOGGER' id='139885869865680'>.error
 +  and   8 = len([{"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2", "type": "drone"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {"created": "1", "updated": "2018-10-28", "deleted": "5", "type": "code"}}, {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {}}, {"_id": "\\\\filer.test\\builds\\dice\\"}, {"_id": "\\\\filer.test\\builds\\dice'\\zero", "_source": {"created": "1", "updated": "2018-10-29 more stuff to sort", "type": "code"}}, ...])
FAILED elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire - AssertionError: assert ([], ['one', 'two', 'three']) == ([], [])
  At index 1 diff: ['one', 'two', 'three'] != []
  Full diff:
  - ([], [])
  + ([], ['one', 'two', 'three'])
FAILED elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire_multi - AssertionError: assert [] == ['\\\\filer.test\\builds\\DICE\\one', '\\\\filer.test\\builds\\DICE\\two']
  Right contains 2 more items, first extra item: '\\\\filer.test\\builds\\DICE\\one'
  Full diff:
  - ['\\\\filer.test\\builds\\DICE\\one', '\\\\filer.test\\builds\\DICE\\two']
  + []
============ 4 failed, 1765 passed, 3 skipped, 1 warning in 19.19s =============

section_end:1751886824:step_script
[0Ksection_start:1751886824:cleanup_file_variables
[0K[0K[36;1mCleaning up project directory and file based variables[0;m[0;m

section_end:1751886825:cleanup_file_variables
[0K[31;1mERROR: Job failed: command terminated with exit code 1
[0;m
16:13:29 2025-07-07 15:13:29 elipy2 [ERROR]: Failed to delete 1 builds:
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: setting code area
16:13:29 2025-07-07 15:13:29 elipy2 [WARNING]: Failed to add code area
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
16:13:29 2025-07-07 15:13:29 elipy2 [ERROR]: 'str' object has no attribute 'id'
16:13:29 Traceback (most recent call last):
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\deleter.py", line 409, in cleanup_builds
16:13:29     pool.map(unpack_expire, clean_up_args)
16:13:29   File "D:\dev\TnT\Bin\Python\3\Lib\multiprocessing\pool.py", line 367, in map
16:13:29     return self._map_async(func, iterable, mapstar, chunksize).get()
16:13:29            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\TnT\Bin\Python\3\Lib\multiprocessing\pool.py", line 774, in get
16:13:29     raise self._value
16:13:29   File "D:\dev\TnT\Bin\Python\3\Lib\multiprocessing\pool.py", line 125, in worker
16:13:29     result = (True, func(*args, **kwds))
16:13:29                     ^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\TnT\Bin\Python\3\Lib\multiprocessing\pool.py", line 48, in mapstar
16:13:29     return list(map(*args))
16:13:29            ^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\deleter.py", line 683, in unpack_expire
16:13:29     utils.expire(
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
16:13:29     value = method(*args, **kwargs)
16:13:29             ^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\expire.py", line 100, in expire
16:13:29     failed_deletion["build"].id,
16:13:29     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29 AttributeError: 'str' object has no attribute 'id'
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: setting code area
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: Skipping azure_fileshare_path_retention files
16:13:29 2025-07-07 15:13:29 elipy2 [WARNING]: Skipping Avalanche db deletion.
16:13:29 2025-07-07 15:13:29 elipy2 [WARNING]: Skipping symstore cleanup.
16:13:29 2025-07-07 15:13:29 elipy2 [ERROR]: 'str' object has no attribute 'id'
16:13:29 NoneType: None
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: setting code area
16:13:29 2025-07-07 15:13:29 elipy2 [WARNING]: Failed to add code area
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
16:13:29 2025-07-07 15:13:29 elipy2 [INFO]: setting code area
16:13:29 Traceback (most recent call last):
16:13:29   File "<frozen runpy>", line 198, in _run_module_as_main
16:13:29   File "<frozen runpy>", line 88, in _run_code
16:13:29   File "D:\dev\Python\virtual\Scripts\elipy.exe\__main__.py", line 7, in <module>
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1130, in __call__
16:13:29     return self.main(*args, **kwargs)
16:13:29            ^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1055, in main
16:13:29     rv = self.invoke(ctx)
16:13:29          ^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1657, in invoke
16:13:29     return _process_result(sub_ctx.command.invoke(sub_ctx))
16:13:29                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1404, in invoke
16:13:29     return ctx.invoke(self.callback, **ctx.params)
16:13:29            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 760, in invoke
16:13:29     return __callback(*args, **kwargs)
16:13:29            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\decorators.py", line 84, in new_func
16:13:29     return ctx.invoke(f, obj, *args, **kwargs)
16:13:29            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 760, in invoke
16:13:29     return __callback(*args, **kwargs)
16:13:29            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\utils\decorators.py", line 68, in wrapper
16:13:29     value = method(*args, **kwargs)
16:13:29             ^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
16:13:29     value = method(*args, **kwargs)
16:13:29             ^^^^^^^^^^^^^^^^^^^^^^^
16:13:29   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\deleter.py", line 154, in cli
16:13:29     raise ELIPYException("Failed to delete all files.")
16:13:29 elipy2.exceptions.ELIPYException: Failed to delete all files
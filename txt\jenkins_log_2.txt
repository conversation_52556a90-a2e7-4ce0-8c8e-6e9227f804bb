Started by timer
Resume disabled by user, switching to high-performance, low-durability mode.
Loading library dst-lib@master
Attempting to resolve master from remote references...
 > git --version # timeout=10
 > git --version # 'git version 2.39.5'
using GIT_SSH to set credentials monkey-commons-ssh-v2
Verifying host key using manually-configured host key entries
 > git ls-remote -- *****************:dre-cobra/dst-ci-configuration.git # timeout=10
Found match: refs/heads/master revision d83f593ae013a7096d16646633dbc9168e3bd8a4
The recommended git tool is: NONE
using credential monkey-commons-ssh-v2
 > git rev-parse --resolve-git-dir /var/jenkins_home/workspace/autoMaintenance.agent@libs/3e730f30c0b4680bf01707194adddd29b0ba0ed689e5f32e85836306bf76a72d/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url *****************:dre-cobra/dst-ci-configuration.git # timeout=10
Fetching without tags
Fetching upstream <NAME_EMAIL>:dre-cobra/dst-ci-configuration.git
 > git --version # timeout=10
 > git --version # 'git version 2.39.5'
using GIT_SSH to set credentials monkey-commons-ssh-v2
Verifying host key using manually-configured host key entries
 > git fetch --no-tags --force --progress -- *****************:dre-cobra/dst-ci-configuration.git +refs/heads/*:refs/remotes/origin/* # timeout=10
Checking out Revision d83f593ae013a7096d16646633dbc9168e3bd8a4 (master)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f d83f593ae013a7096d16646633dbc9168e3bd8a4 # timeout=10
Commit message: "Adding a new branch and new tests for it"
[Pipeline] Start of Pipeline
[Pipeline] stage
[Pipeline] { (Automaintenance Node if failing with un-recovered BFA)
[Pipeline] node
Running on Jenkins
 in /var/jenkins_home/workspace/autoMaintenance.agent
[Pipeline] {
[Pipeline] echo
Checking for jobs waiting for cloud VM executors...
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Job getQueueItem. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getInQueueSince. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getAssignedLabel. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Job avalanche_maintenance has been waiting for 471.9501333333 minutes
[Pipeline] echo
Job avalanche_maintenance is waiting for label: bct3-a32274
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Job getQueueItem. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getInQueueSince. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getAssignedLabel. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.all.start has been waiting for 291.0658166667 minutes
[Pipeline] echo
Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.all.start is waiting for label: built-in
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Job getQueueItem. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getInQueueSince. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getAssignedLabel. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Job getQueueItem. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getInQueueSince. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getAssignedLabel. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.all.start has been waiting for 195.0660666667 minutes
[Pipeline] echo
Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.all.start is waiting for label: built-in
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Job getQueueItem. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getInQueueSince. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getAssignedLabel. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Job trunk-code-dev.autotest.pt_perf_highpriority_flythroughs_final_setup.xbsx.parallel.job-1 has been waiting for 123.7533333333 minutes
[Pipeline] echo
Job trunk-code-dev.autotest.pt_perf_highpriority_flythroughs_final_setup.xbsx.parallel.job-1 is waiting for label: poolbuild_eala&&xbsx
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Job getQueueItem. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getInQueueSince. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method hudson.model.Queue$Item getAssignedLabel. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 has been waiting for 38.7378666667 minutes
[Pipeline] echo
Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 is waiting for label: unittests_engine&&tool
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild isBuilding. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job agent.reboot ...
[Pipeline] echo
Build  #35272 failed with "Remote call on JNLP4-connect connection (DRE)"
[Pipeline] echo
bct3-041468need to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Processing Job agent.reboot ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job avalanche_maintenance_azure ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk-sub.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job.24271068.24271068 failed with "partitions with duplicate GUIDs found (DRE)"
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job.24271068.24271068 failed with "partitions with duplicate GUIDs found (DRE)"
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job.24271068.24271068 failed with "partitions with duplicate GUIDs found (DRE)"
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job.24271068.24271068 failed with "partitions with duplicate GUIDs found (DRE)"
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_auto.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_qv.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_qv.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_qv.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_qv.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.lkg_qv.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_func_playtest.win64.parallel.job-1 ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.pt_func_playtest.win64.parallel.job-1.24271068.24271068 failed with "No connection could be made because the target machine actively refused it"
[Pipeline] echo
bct3-0e5e44need to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job dev-na-to-trunk.autotest.pt_func_playtest.all.start need to rebuild 
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_func_playtest.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_func_playtest.xbsx.parallel.job-1 ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.pt_func_playtest.xbsx.parallel.job-1.24271068.24271068 failed with "No connection could be made because the target machine actively refused it"
[Pipeline] echo
bct3-1127b2need to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job dev-na-to-trunk.autotest.pt_func_playtest.all.start need to rebuild 
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_func_playtest.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_flythroughs_final_setup.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_flythroughs_final_setup.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_flythroughs_final_setup.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_flythroughs_final_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_flythroughs_final_setup.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_flythroughs_final_setup2.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_flythroughs_final_setup2.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_flythroughs_final_setup2.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_lowpriority_final_setup.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_minspec_performance_setup.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_minspec_performance_setup.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_minspec_performance_setup.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_minspec_performance_setup.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_minspec_performance_setup.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.pt_perf_performance_setup.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Build  dev-na-to-trunk.autotest.unittests_engine.tool.parallel.job-1.now.24271068 failed with "An existing connection was forcibly closed by the remote host"
[Pipeline] echo
bct3-e598ccneed to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job dev-na-to-trunk.autotest.unittests_engine.all.start need to rebuild 
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.ps5.parallel.job-2 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2 ...
[Pipeline] echo
Build  trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2.now.24037412 failed with "An existing connection was forcibly closed by the remote host"
[Pipeline] echo
bct3-d7991aneed to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.all.start need to rebuild 
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_static.xbsx.parallel.job-2 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2 ...
[Pipeline] echo
Build  trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2.now.24037412 failed with "An existing connection was forcibly closed by the remote host"
[Pipeline] echo
bct3-0ecb40need to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.all.start need to rebuild 
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_asan_tool.win64.parallel.job-2 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev-sanitizers.autotest.unittests_ubsan_static.ps5.parallel.job-2 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frosted_vstests.win64.AllVSTests.job ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job ...
[Pipeline] echo
Build  trunk-code-dev.autotest.frostedtests_extended.win64.frostedtest_EcsOrbitalPerformance.job.24325976.24325976 failed with "partitions with duplicate GUIDs found (DRE)"
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job ...
[Pipeline] echo
Build  trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_efw_core_workflow_tests.job.24325976.24325976 failed with "partitions with duplicate GUIDs found (DRE)"
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job ...
[Pipeline] echo
Build  trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job.24325976.24325976 failed with "partitions with duplicate GUIDs found (DRE)"
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.frostedtests_fbapi.win64.frosted_scene_workflow_tests.job ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-2 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.win64.parallel.job-2 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.lkg_auto.xbsx.parallel.job-2 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.pt_perf_highpriority_performance_setup.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.pt_perf_highpriority_performance_setup.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.pt_perf_performance_setup.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Build  trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1.now.24325976 failed with "An existing connection was forcibly closed by the remote host"
[Pipeline] echo
bct3-e598ccneed to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job trunk-code-dev.autotest.unittests_engine.all.start need to rebuild 
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-code-dev.autotest.unittests_engine.tool.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-content-dev.autotest.lkg_checkmate.win64.parallel.job-1 ...
[Pipeline] echo
Build  #5591 failed with "Remote call on JNLP4-connect connection (DRE)"
[Pipeline] echo
bct3-041468need to be rebooted ...
[Pipeline] build (Scheduling agent.reboot)
Scheduling project: agent.reboot

[Pipeline] echo
Job trunk-content-dev.autotest.lkg_checkmate.all.start need to rebuild 
[Pipeline] echo
Job trunk-content-dev.autotest.lkg_checkmate.all.start are rebuilding last failed build ... 
[Pipeline] echo
Processing Job trunk-content-dev.autotest.lkg_checkmate.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-content-dev.bilbo.register-bfdata-autotestutils ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.frostedtests.win64.FrostEdTest_BFWorkflowsTechArt.job ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_auto.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_bootanddeploy_tool.tool.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_qv.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_qv.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_qv.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_qv.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.lkg_qv.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.performance_medium_priority.ps5.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.performance_medium_priority.ps5.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.performance_medium_priority.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.performance_medium_priority.xbsx.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.performance_medium_priority.xbsx.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.pt_perf_minspec_trunk_to_dev_na_final_setup.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.pt_perf_minspec_trunk_to_dev_na_final_setup.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.unittests.win64.parallel.job-1 ...
[Pipeline] echo
Processing Job trunk-to-dev-na.autotest.unittests.win64.parallel.job-1 ...
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
Scripts not permitted to use method jenkins.model.HistoricalBuild getResult. Administrators can decide whether to approve or reject this signature.
[Pipeline] }
[Pipeline] // node
[Pipeline] }
[Pipeline] // stage
[Pipeline] End of Pipeline
Finished: SUCCESS
